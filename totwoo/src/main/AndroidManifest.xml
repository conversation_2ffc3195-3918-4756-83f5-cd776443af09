<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />


    <!--自定义权限，允许程序读取公共数据-->
    <uses-permission android:name="com.huawei.appmarket.service.commondata.permission.GET_COMMON_DATA"/>

    <uses-permission android:name="com.huawei.systemmanager.permission.ACCESS_INTERFACE" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />

    <uses-permission android:name="com.totwoo.totwoo.permission.MIPUSH_RECEIVE" />
    <!-- Required  一些系统要求的权限，如访问网络等 -->

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />

<!--android 11-->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- Request legacy Bluetooth permissions on older devices. -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />


    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- 这个权限用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- 这个权限用于访问GPS定位 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- 用于访问wifi网络信息，wifi信息会用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <!-- 获取运营商信息，用于支持提供运营商信息相关的接口 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 这个权限用于获取wifi的获取权限，wifi信息会用来进行网络定位 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!-- 用于读取手机当前的状态 -->
    <uses-permission android:name="android.permission.CALL_PHONE" />

    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <!-- 多媒体 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- 极光相关 -->
    <uses-permission android:name="com.totwoo.totwoo.permission.JPUSH_MESSAGE" />
    <permission
        android:name="com.totwoo.totwoo.permission.JPUSH_MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!--<uses-permission android:name="android.permission.WRITE_SETTINGS" />-->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <!-- <uses-permission android:name="android.permission.CALL_PHONE"/> -->

    <!-- 来电 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <!-- 重力传感器, 实现摇一摇功能 -->
    <uses-permission android:name="android.hardware.sensor.accelerometer" />
    <!--    &lt;!&ndash; 安装包 &ndash;&gt; todo 谷歌需要去掉这个-->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <!-- 电池优化白名单 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <uses-permission android:name="android.permission.NFC" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />

    <!-- 精确闹钟权限 - 用于蓝牙重连和保活检查 -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <application
        android:name="com.totwoo.totwoo.ToTwooApplication"
        android:allowBackup="true"
        android:fullBackupContent="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:localeConfig="@xml/locales_config"
        android:supportsRtl="false"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="LockedOrientationActivity"
        tools:replace="android:allowBackup,supportsRtl"
        tools:targetApi="tiramisu">

        <activity
            android:name="com.totwoo.totwoo.activity.WelcomeActivity"
            android:exported="true"
            android:theme="@style/AppTheme.SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

<!--        <meta-data-->
<!--            android:name="design_width_in_dp"-->
<!--            android:value="375"/>-->
<!--        <meta-data-->
<!--            android:name="design_height_in_dp"-->
<!--            android:value="812"/>-->

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_launcher_small" />

        <!--<activity-->
        <!--android:name=".activity.HomeActivity"-->
        <!--android:launchMode="singleTask"-->
        <!--android:screenOrientation="portrait"-->
        <!--android:theme="@style/HomePageTheme">-->
        <!--<intent-filter>-->
        <!--<data android:scheme="tencent1104740113" />-->
        <!--<action android:name="android.intent.action.VIEW" />-->
        <!--<category android:name="android.intent.category.BROWSABLE" />-->
        <!--<category android:name="android.intent.category.DEFAULT" />-->
        <!--</intent-filter>-->

        <!--</activity>-->

        <activity
            android:name=".activity.homeActivities.DefaultHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.homeActivities.LoveHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.homeActivities.MemoryHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.homeActivities.WishHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.homeActivities.ReminderHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.homeActivities.LollipopHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.homeActivities.AngelHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.homeActivities.SecurityHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.homeActivities.NFCHomeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.memory.MemoryPageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.GuidanceActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.LoginAndPasswordActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activity.PasswordLoginActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />

        <activity
            android:name=".activity.AutoLoginActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name=".activity.RegisterActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />

        <activity
            android:name=".activity.ForgetPasswordPhoneActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ForgetPasswordCodeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.SetPasswordActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ModifyPasswordActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.UserInfoSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.InitInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.JewelryConnectActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.JewelrySelectActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".fragment.lovecode.HeartSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ConnectInfoActivity"
            android:screenOrientation="portrait" />
        <!--<activity-->
        <!--android:name=".activity.TheHeartActivity"-->
        <!--android:launchMode="singleTop"-->
        <!--android:screenOrientation="portrait" />-->
        <!--<activity-->
        <!--android:name=".activity.heart.NewHeartActivity"-->
        <!--android:launchMode="singleTask"-->
        <!--android:screenOrientation="portrait" />-->
        <activity
            android:name=".activity.ConstellationActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!--        <activity-->
        <!--            android:name=".activity.wish.WishWebActivity"-->
        <!--            android:screenOrientation="portrait" />-->
        <activity
            android:name=".activity.memory.MemoryAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.security.SecurityReportListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.security.SecurityNewListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.security.SafeJewSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.security.FakeCallActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.security.EmergencyDocActivity"
            android:configChanges="keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/TimChatAppTheme"
            android:windowSoftInputMode="adjustResize|stateHidden" />
<!--        <activity-->
<!--            android:name=".activity.HistoryActivity"-->
<!--            android:screenOrientation="portrait" />-->
        <activity
            android:name=".activity.HistoryTwooActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".fragment.lovecode.HeartPhotoFolderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.SedentaryReminderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.StepCounterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.LoveSpaceNotifyStatusActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.SleepStatisticActivity"
            android:theme="@style/TransTheme" />
        <activity
            android:name=".activity.BrightModeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.BrightMusicActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.BrightPartMusicActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.LoveSpacePinkActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.AddLoveNotifyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.MeSettingActivity"
            android:configChanges="orientation|keyboardHidden"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.WebActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.PeriodSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.security.GuardActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ReminderTouchHintActivity"
            android:theme="@style/TransTheme" />
        <activity
            android:name=".activity.security.ImeiInfoUpdateActivity"
            android:theme="@style/TransTheme" />
        <activity
            android:name=".activity.FeedbackQRCodeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.WaterTimeSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.CustomOrderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.WishCreatTypeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.LottieTestActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.WishAddVoiceInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.WishAddVideoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.WishAddTextInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.WishCardGalleryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.WishDetailInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.MediaPreviewActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.WishListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.giftMessage.SendGiftGalleryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.giftMessage.GiftInfoAddActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TimChatAppTheme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.giftMessage.CommonVideoRecorderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.CalorieActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.ContactsSelectActivity"
            android:configChanges="keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/TimChatAppTheme"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activity.ContactsActivityForGift"
            android:configChanges="keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/TimChatAppTheme"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activity.ContactsActivityForCall"
            android:configChanges="keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/TimChatAppTheme"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name=".activity.ContactsActivityForEmergency"
            android:configChanges="keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/TimChatAppTheme"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name=".activity.ContactsActivityForReport"
            android:configChanges="keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/TimChatAppTheme"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name=".activity.giftMessage.GiftVoiceAddActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.giftMessage.GiftDataActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"/>
        <activity
            android:name=".activity.giftMessage.GiftMessageListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.wish.WishAddInfoActivity"
            android:configChanges="keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/TimChatAppTheme"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activity.CameraActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.VideoSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.CustomNotifyListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.NewUserBenfitActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.CustomNotifyEditActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.LoveNotifyEditActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.NotifyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.security.SecurityHintActivity"
            android:screenOrientation="portrait" />
        <!--        <activity-->
        <!--            android:name=".activity.security.SecurityPayActivity"-->
        <!--            android:screenOrientation="portrait" />-->
        <!--        <activity-->
        <!--            android:name=".activity.security.SecurityPayRecordActivity"-->
        <!--            android:screenOrientation="portrait" />-->
        <activity
            android:name=".activity.LoveNotifyListActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.WebViewActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait" />

        <service
            android:name=".service.MessengerService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.totwoo.totwoo.messageservice" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <activity
            android:name=".activity.TheHeartChooseActivity"
            android:label="@string/title_activity_the_heart_help"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.totwoo.totwoo.tim.TUIC2CChatActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name="com.totwoo.totwoo.tim.TimNotifySettingActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name=".activity.TheHeartManageActivity"
            android:label="@string/title_activity_the_heart_help"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.LoveManageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ShareActivity"
            android:label="@string/title_activity_main"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.LanguageSettingActivity"
            android:screenOrientation="portrait" />

        <service
            android:name=".service.WearClientService"
            android:exported="true">
            <intent-filter>
                <action
                    android:name="com.google.android.gms.wearable.BIND_LISTENER"
                    tools:ignore="WearableBindListener" />
            </intent-filter>
        </service>

        <service
            android:name=".service.KeepAliveService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="connectedDevice">
            <intent-filter>
                <action android:name="com_totwoo_towooo_jewconnect_service_start" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

<!--        <service-->
<!--            android:name="androidx.work.impl.foreground.SystemForegroundService"-->
<!--            android:foregroundServiceType="connectedDevice"-->
<!--            tools:node="merge" />-->

        <service
            android:name=".service.BrightMusicPlayService"
            android:enabled="true" />
        <service
            android:name=".service.TotwooPlayService"
            android:enabled="true" />

        <!-- 百度定位相关服务 -->
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="o3tSPWTFj21oDCPt3LSV6meT" />
        <!-- facebook相关服务 -->
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="2165854100108506" />

        <meta-data
            android:name="com.lk.lbsapi.API_KEY"
            android:value="EE1709618671396203188EBE0AF582A4052ACDE25D0E829BFE1MXGTGZZT25639" />

        <meta-data
            android:name="android.max_aspect"
            android:value="2.2" />

        <!--        &lt;!&ndash; mipush &ndash;&gt;-->
        <!--        <service-->
        <!--            android:name="com.xiaomi.push.service.XMJobService"-->
        <!--            android:enabled="true"-->
        <!--            android:exported="false"-->
        <!--            android:permission="android.permission.BIND_JOB_SERVICE"-->
        <!--            android:process=":MiPushService" />-->
        <!--        <service-->
        <!--            android:name="com.xiaomi.push.service.XMPushService"-->
        <!--            android:enabled="true"-->
        <!--            android:process=":MiPushService" />-->
        <!--        <service-->
        <!--            android:name="com.xiaomi.mipush.sdk.PushMessageHandler"-->
        <!--            android:enabled="true"-->
        <!--            android:exported="true" />-->
        <!--        <service-->
        <!--            android:name="com.xiaomi.mipush.sdk.MessageHandleService"-->
        <!--            android:enabled="true" />-->

        <!-- Since JCore2.0.0 Required SDK核心功能-->
        <!-- 可配置android:process参数将Service放在其他进程中；android:enabled属性不能是false -->
        <!-- 这个是自定义Service，要继承极光JCommonService，可以在更多手机平台上使得推送通道保持的更稳定 -->
        <service
            android:name=".service.JPushService"
            android:enabled="true"
            android:exported="false"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jiguang.user.service.action" />
            </intent-filter>
        </service>

        <!-- Required since 5.2.0 -->
        <!-- 新的 tag/alias 接口结果返回需要开发者配置一个自定义的Service -->
        <!-- 5.2.0开始所有事件将通过该类回调 -->
        <!-- 该广播需要继承 JPush 提供的 JPushMessageService 类, 并如下新增一个 Intent-Filter -->
        <receiver
            android:name=".receiver.JpushReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.RECEIVER_MESSAGE" />
                <category android:name="com.totwoo.totwoo" />
            </intent-filter>
        </receiver>

        <service
            android:name=".service.AppNotifyRemindService"
            android:exported="true"
            android:label="totwoo"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <!-- 微信分享回调 -->
<!--        <activity-->
<!--            android:name=".wxapi.WXEntryActivity"-->
<!--            android:configChanges="keyboardHidden|orientation|screenSize"-->
<!--            android:exported="true"-->
<!--            android:screenOrientation="portrait"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar" />-->

<!--        <activity-->
<!--            android:name=".wxapi.WXPayEntryActivity"-->
<!--            android:exported="true"-->
<!--            android:launchMode="singleTop" />-->

        <receiver
            android:name=".receiver.TotwooSendReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com_totwoo_send_totwoo" />
                <!-- 通过通知栏或者 wear 回复 totwoo 的 Action -->

                <action android:name="send.sedentary.reminder" />
                <!-- 接收久坐提醒通知提醒 -->
            </intent-filter>

            <intent-filter>
                <action android:name="android.bluetooth.adapter.action.STATE_CHANGED" />
                <action android:name="android.bluetooth.headset.profile.action.CONNECTION_STATE_CHANGED" />
                <action android:name="android.bluetooth.a2dp.profile.action.CONNECTION_STATE_CHANGED" />
                <action android:name="android.bluetooth.device.action.ACL_CONNECTED" />
                <action android:name="android.bluetooth.device.action.ACL_DISCONNECTED" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receiver.CallRemindRecaiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.PHONE_STATE" />
                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
            </intent-filter>
        </receiver>

        <service
            android:name=".service.DfuService"
            android:foregroundServiceType="connectedDevice"
            android:exported="true">
            <intent-filter>
                <action android:name="no.nordicsemi.android.action.DFU_UPLOAD" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <activity
            android:name=".service.NotificationActivity"
            android:screenOrientation="portrait" />

        <!--        &lt;!&ndash; 【必须】消息收发service &ndash;&gt;-->
        <!--        <service-->
        <!--            android:name="com.tencent.qalsdk.service.QalService"-->
        <!--            android:exported="true"-->
        <!--            android:process=":QALSERVICE" />-->
        <!--        <service-->
        <!--            android:name="com.tencent.qalsdk.service.QalAssistService"-->
        <!--            android:process=":QALSERVICE" />-->

        <!--        &lt;!&ndash; 【必须】 离线消息广播接收器 &ndash;&gt;-->
        <!--        <receiver-->
        <!--            android:name="com.tencent.qalsdk.QALBroadcastReceiver"-->
        <!--            android:exported="true">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.tencent.qalsdk.broadcast.qal" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->
        <!--        <receiver-->
        <!--            android:name="com.tencent.qalsdk.core.NetConnInfoCenter"-->
        <!--            android:exported="true"-->
        <!--            android:process=":QALSERVICE">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.intent.action.BOOT_COMPLETED" />-->
        <!--            </intent-filter>-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />-->
        <!--            </intent-filter>-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.intent.action.TIME_SET" />-->
        <!--            </intent-filter>-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.intent.action.TIMEZONE_CHANGED" />-->
        <!--            </intent-filter>-->

        <!--            &lt;!&ndash; ImSDK 3.0.2 后添加 &ndash;&gt;-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.tencent.qalsdk.service.TASK_REMOVED" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->

        <!-- <activity -->
        <!-- android:name=".activity.JewelryQuantityActivity" -->
        <!-- android:label="@string/title_activity_jewelry_quantity" -->
        <!-- android:screenOrientation="portrait"/> -->
        <activity
            android:name=".activity.AboutActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryEnterActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryListActivity"
            android:label="@string/title_activity_about"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryPhotoAddActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".activity.memory.MemoryPhotoSelectActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryPhotoFolderActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryPhotoShowActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryVedioActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryVedioActivity2"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryVedioShowActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryAudioActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryAudioActivity2"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemoryAudioShowActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemorySayActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.memory.MemorySayShowActivity"
            android:label="@string/title_activity_about"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.OpinionFeedbackActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysVisible" />
        <activity
            android:name=".activity.AddCustomNotifyActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activity.JewelryInfoActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.WeiboAuthActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.JewelryPairedListActivity"
            android:screenOrientation="portrait" />
        <!-- <activity -->
        <!-- android:name=".activity.NotificationSettingActivity" -->
        <!-- android:screenOrientation="portrait"/> -->
        <activity
            android:name=".activity.SelectCityActivity"
            android:label="@string/title_activity_select_city"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.StepTargetSettingActivity"
            android:label="@string/title_activity_step_target_setting"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.CountryCodeListActivity"
            android:label="@string/title_activity_country_code_list"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.JewelryOTAActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.JewelryOTATestActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.TestColorActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.TestSleepActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.TestTogetherTimeActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.ShareConstellationActivity"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ShareStepActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.QianDetailActivity"
            android:label="@string/title_activity_qian_detail"
            android:theme="@style/TransTheme" />
        <activity
            android:name=".activity.MessageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.YesNoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ContactsListActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.totwoo.totwoo.activity.AppNotificationsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.CallRemindSetActivity"
            android:screenOrientation="portrait" />
<!--        <activity-->
<!--            android:name=".activity.ShareViewActivity"-->
<!--            android:screenOrientation="portrait" />-->
        <activity
            android:name=".activity.QianShareActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.QianActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ConnectHelpActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.HelpHelpActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.NotifySettingActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.NotifyTotwooActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.NotifyCallActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.TestActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.VibrationIntensityActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.StepSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.LoveCertificationActivity"
            android:screenOrientation="portrait" />
        <activity android:name=".activity.together.MainTogetherActivity" />
        <activity android:name=".activity.together.TogetherSelectActivity" />
        <activity android:name=".activity.together.FootPrintSelectActivity" />
        <activity android:name=".activity.together.FootPrintShareActivity" />
        <activity
            android:name=".activity.nfc.NfcBoundActivity"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait">

            <!--            <intent-filter>-->
            <!--                <action android:name="android.nfc.action.NDEF_DISCOVERED" />-->
            <!--                <category android:name="android.intent.category.DEFAULT" />-->
            <!--                <data-->
            <!--                    android:host="m.totwoo.com"-->
            <!--                    android:pathPrefix="/nfc"-->
            <!--                    android:scheme="https" />-->
            <!--            </intent-filter>-->

        </activity>

        <activity
            android:name=".activity.nfc.NfcSecretSelectActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.nfc.NfcMediaListActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.nfc.NfcMediaDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.nfc.NfcSecretPasswdActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.nfc.NfcDomainActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.nfc.NfcCardEditActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.SelectCoupleActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:windowSoftInputMode="stateAlwaysVisible"/>

        <activity
            android:name=".activity.CoupleRequestInfoActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.SetPermissionActivity"
            android:screenOrientation="portrait" />


        <activity
            android:name=".activity.ReminderSettingsActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.nfc.NfcCardPrewActivity"
            android:screenOrientation="portrait" />
        <activity android:name=".utils.PictureCropperActivity"
            android:screenOrientation="portrait"/>
        <activity android:name="com.totwoo.totwoo.activity.DebugActivity"
            android:screenOrientation="portrait"/>

        <activity android:name="com.totwoo.totwoo.activity.NotificationGuideActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".activity.CustomTouchColorActivity"
            android:screenOrientation="portrait" />

        <service android:name=".service.WaterTimeAlarmService" />
        <service android:name=".service.CustomNotifyAlarmService" />

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider2165854100108506"
            android:exported="true" />

        <meta-data
            android:name="com.tencent.rdm.uuid"
            android:value="83b3186d-0ef4-40e4-83ca-ced776b1ee1e" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

<!--        <activity-->
<!--            android:name="com.mob.guard.MobTranPullUpActivity"-->
<!--            android:exported="true"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.mob.open.app_20000" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
<!--        <activity-->
<!--            android:name="com.mob.id.MobIDActivity"-->
<!--            android:exported="true"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.mob.id.app_30000" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
<!--        <activity-->
<!--            android:name="com.mob.guard.MobTranPullLockActivity"-->
<!--            android:exported="true"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.mob.open.app_lk" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
<!--        <activity-->
<!--            android:name="com.mob.id.MobIDSYActivity"-->
<!--            android:exported="true"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.mob.id.app_lk" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
<!--        </activity>-->

<!--        <service-->
<!--            android:name="com.mob.guard.MobGuardPullUpService"-->
<!--            android:enabled="true"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.mob.intent.MOB_GUARD_SERVICE" />-->
<!--            </intent-filter>-->
<!--        </service>-->
<!--        <service-->
<!--            android:name="com.mob.id.MobIDService"-->
<!--            android:enabled="true"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.mob.intent.MOB_ID_SERVICE" />-->
<!--            </intent-filter>-->
<!--        </service>-->

<!--        <meta-data-->
<!--            android:name="mob_guard_version"-->
<!--            android:value="30000" />-->
<!--        <meta-data-->
<!--            android:name="mob_id_ver"-->
<!--            android:value="30000" />-->

<!--        <service-->
<!--            android:name="com.mob.MobACService"-->
<!--            android:enabled="true"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.mob.service.action.MOB_AC_SERVICE" />-->
<!--            </intent-filter>-->
<!--        </service>-->

        <!-- 蓝牙保活JobService -->
        <service android:name=".service.KeepAliveJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="false" />

        <!-- 系统事件广播接收器 -->
        <receiver android:name=".receiver.KeepAliveSystemReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- 自动存储应用内选择的语言-->
        <service
            android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
            android:enabled="false"
            android:exported="false">
            <meta-data
                android:name="autoStoreLocales"
                android:value="true" />
        </service>

        <!-- Trigger Google Play services to install the backported photo picker module. -->
        <service android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false"
            tools:ignore="MissingClass">
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>
            <meta-data android:name="photopicker_activity:0:required" android:value="" />
        </service>


    </application>
</manifest>
