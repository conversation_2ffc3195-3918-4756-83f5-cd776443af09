package com.totwoo.totwoo.service;

import android.annotation.TargetApi;
import android.app.job.JobInfo;
import android.app.job.JobParameters;
import android.app.job.JobScheduler;
import android.app.job.JobService;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.BatteryManager;
import android.os.Build;
import android.text.TextUtils;

import com.blankj.utilcode.util.RomUtils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.PreferencesUtils;

/**
 * 蓝牙保活JobService
 * 
 * 功能：
 * 1. 系统级保活备份
 * 2. 设备重启后恢复
 * 3. 网络状态变化监听
 * 4. 服务被杀后重启
 */
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
public class KeepAliveJobService extends JobService {

    private static final String TAG = "KeepAliveJobService";
    private static final int JOB_ID = 1001;

    // 添加执行间隔控制
    private static final String PREF_LAST_EXECUTION_TIME = "job_last_execution_time";
    private static final long MIN_EXECUTION_INTERVAL = 5 * 60 * 1000; // 5分钟最小间隔
    private static final long XIAOMI_MIN_EXECUTION_INTERVAL = 10 * 60 * 1000; // 小米设备10分钟最小间隔

    // 添加执行计数控制
    private static final String PREF_EXECUTION_COUNT = "job_execution_count";
    private static final int MAX_EXECUTIONS_PER_HOUR = 6; // 每小时最多执行6次
    private static final int XIAOMI_MAX_EXECUTIONS_PER_HOUR = 3; // 小米设备每小时最多3次
    
    @Override
    public boolean onStartJob(JobParameters params) {
        String deviceInfo = RomUtils.isXiaomi() ? "小米设备" : "其他设备";
        LogUtils.d(TAG, "JobScheduler保活检查开始 - " + deviceInfo + " (设备: " + Build.MODEL + ")");

        // 检查执行频率限制
        if (!canExecuteJob()) {
            LogUtils.w(TAG, "JobScheduler执行频率受限，跳过本次检查");
            jobFinished(params, false);
            return true;
        }

        // 异步执行检查任务
        new Thread(() -> {
            try {
                boolean shouldReschedule = performKeepAliveCheck();

                // 记录执行时间
                recordJobExecution();

                // 根据执行结果决定是否重新调度
                if (shouldReschedule) {
                    scheduleNextJobWithDelay();
                }

                jobFinished(params, false); // 成功完成
                LogUtils.d(TAG, "JobScheduler保活检查完成 - " + deviceInfo);

            } catch (Exception e) {
                LogUtils.e(TAG, "JobScheduler保活检查失败: " + e.getMessage());
                jobFinished(params, true); // 失败，需要重试
            }
        }).start();

        return true; // 异步执行
    }
    
    @Override
    public boolean onStopJob(JobParameters params) {
        LogUtils.d(TAG, "JobScheduler被系统停止");
        return true; // 需要重新调度
    }
    
    /**
     * 执行保活检查
     * @return 是否需要重新调度任务
     */
    private boolean performKeepAliveCheck() {
        LogUtils.d(TAG, "执行JobScheduler保活检查");

        // 1. 检查是否有配对的蓝牙设备
        if (!hasBluetoothJewelry()) {
            LogUtils.d(TAG, "没有配对的蓝牙首饰，跳过保活检查");
            return false; // 没有设备时不需要频繁检查
        }

        // 2. 检查KeepAliveService状态
        boolean serviceRestarted = checkAndRestartKeepAliveService();

        // 3. 检查蓝牙连接状态
        boolean bluetoothReconnected = checkBluetoothConnection();

        // 4. 检查电池状态
        checkBatteryStatus();

        // 5. 根据设备类型和执行结果决定是否需要重新调度
        boolean needReschedule = shouldRescheduleJob(serviceRestarted, bluetoothReconnected);

        LogUtils.d(TAG, "JobScheduler保活检查完成，需要重新调度: " + needReschedule);
        return needReschedule;
    }
    
    /**
     * 检查是否有蓝牙首饰
     */
    private boolean hasBluetoothJewelry() {
        String jewelryName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        return !TextUtils.isEmpty(jewelryName) && BleParams.isBluetoothJewelry(jewelryName);
    }
    
    /**
     * 检查并重启KeepAliveService
     * @return 是否重启了服务
     */
    private boolean checkAndRestartKeepAliveService() {
        boolean isServiceRunning = ToTwooApplication.mService != null;
        LogUtils.d(TAG, "KeepAliveService状态: " + (isServiceRunning ? "运行中" : "未运行"));

        if (!isServiceRunning) {
            LogUtils.w(TAG, "KeepAliveService未运行，JobScheduler触发重启");
            try {
                Intent serviceIntent = new Intent(this, KeepAliveService.class);
                serviceIntent.setAction("JOBSCHEDULER_RESTART");

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    startForegroundService(serviceIntent);
                } else {
                    startService(serviceIntent);
                }

                LogUtils.d(TAG, "KeepAliveService重启成功");
                return true;
            } catch (Exception e) {
                LogUtils.e(TAG, "KeepAliveService重启失败: " + e.getMessage());
                return false;
            }
        }
        return false;
    }
    
    /**
     * 检查蓝牙连接状态
     * @return 是否触发了重连
     */
    private boolean checkBluetoothConnection() {
        int bluetoothState = JewInfoSingleton.getInstance().getConnectState();
        LogUtils.d(TAG, "蓝牙连接状态: " + getBluetoothStateDescription(bluetoothState));

        if (bluetoothState == JewInfoSingleton.STATE_DISCONNECTED ||
            bluetoothState == JewInfoSingleton.STATE_UNPAIRED) {
            LogUtils.w(TAG, "蓝牙未连接，JobScheduler触发重连");
            try {
                BluetoothManage.getInstance().reconnect(false);
                return true;
            } catch (Exception e) {
                LogUtils.e(TAG, "蓝牙重连失败: " + e.getMessage());
                return false;
            }
        }
        return false;
    }
    
    /**
     * 检查电池状态
     */
    private void checkBatteryStatus() {
        BatteryManager batteryManager = (BatteryManager) getSystemService(BATTERY_SERVICE);
        if (batteryManager == null) return;
        
        int batteryLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
        boolean isCharging = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_STATUS) 
            == BatteryManager.BATTERY_STATUS_CHARGING;
        
        LogUtils.d(TAG, "电池状态: " + batteryLevel + "%, 充电中: " + isCharging);
        
        // 电池极低时减少JobScheduler频率
        if (batteryLevel < 10 && !isCharging) {
            LogUtils.d(TAG, "电池极低，JobScheduler进入省电模式");
            // 可以调整调度策略
        }
    }
    
    /**
     * 检查是否可以执行Job
     */
    private boolean canExecuteJob() {
        long currentTime = System.currentTimeMillis();
        long lastExecutionTime = PreferencesUtils.getLong(this, PREF_LAST_EXECUTION_TIME, 0);

        // 检查最小执行间隔
        long minInterval = RomUtils.isXiaomi() ? XIAOMI_MIN_EXECUTION_INTERVAL : MIN_EXECUTION_INTERVAL;
        if (currentTime - lastExecutionTime < minInterval) {
            LogUtils.d(TAG, "执行间隔过短，跳过执行。间隔: " + (currentTime - lastExecutionTime) + "ms");
            return false;
        }

        // 检查每小时执行次数限制
        int maxExecutions = RomUtils.isXiaomi() ? XIAOMI_MAX_EXECUTIONS_PER_HOUR : MAX_EXECUTIONS_PER_HOUR;
        int executionCount = PreferencesUtils.getInt(this, PREF_EXECUTION_COUNT, 0);
        long oneHourAgo = currentTime - 60 * 60 * 1000;

        if (lastExecutionTime > oneHourAgo && executionCount >= maxExecutions) {
            LogUtils.d(TAG, "每小时执行次数达到上限，跳过执行。次数: " + executionCount);
            return false;
        }

        // 如果超过一小时，重置计数
        if (lastExecutionTime <= oneHourAgo) {
            PreferencesUtils.put(this, PREF_EXECUTION_COUNT, 0);
        }

        return true;
    }

    /**
     * 记录Job执行
     */
    private void recordJobExecution() {
        long currentTime = System.currentTimeMillis();
        PreferencesUtils.put(this, PREF_LAST_EXECUTION_TIME, currentTime);

        int executionCount = PreferencesUtils.getInt(this, PREF_EXECUTION_COUNT, 0);
        PreferencesUtils.put(this, PREF_EXECUTION_COUNT, executionCount + 1);

        LogUtils.d(TAG, "记录Job执行，当前小时内执行次数: " + (executionCount + 1));
    }

    /**
     * 判断是否需要重新调度Job
     */
    private boolean shouldRescheduleJob(boolean serviceRestarted, boolean bluetoothReconnected) {
        // 小米设备更保守的调度策略
        if (RomUtils.isXiaomi()) {
            // 如果服务或蓝牙有问题才重新调度
            return serviceRestarted || bluetoothReconnected;
        } else {
            // 其他设备正常调度
            return true;
        }
    }

    /**
     * 延迟调度下一次任务
     */
    private void scheduleNextJobWithDelay() {
        // 小米设备使用更长的延迟
        long delay = RomUtils.isXiaomi() ? 20 * 60 * 1000 : 15 * 60 * 1000; // 小米20分钟，其他15分钟

        new Thread(() -> {
            try {
                Thread.sleep(delay);
                scheduleJob(this);
                LogUtils.d(TAG, "延迟调度Job完成，延迟时间: " + delay + "ms");
            } catch (InterruptedException e) {
                LogUtils.e(TAG, "延迟调度被中断: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * 获取蓝牙状态描述
     */
    private String getBluetoothStateDescription(int state) {
        switch (state) {
            case JewInfoSingleton.STATE_UNPAIRED:
                return "未配对";
            case JewInfoSingleton.STATE_DISCONNECTED:
                return "已断开";
            case JewInfoSingleton.STATE_CONNECTED:
                return "已连接";
            case JewInfoSingleton.STATE_RECONNECTING:
                return "重连中";
            default:
                return "未知状态(" + state + ")";
        }
    }
    
    /**
     * 调度JobScheduler任务
     */
    public static void scheduleJob(Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            LogUtils.d(TAG, "Android版本低于5.0，不支持JobScheduler");
            return;
        }

        JobScheduler jobScheduler = (JobScheduler) context.getSystemService(JOB_SCHEDULER_SERVICE);
        if (jobScheduler == null) {
            LogUtils.e(TAG, "JobScheduler服务不可用");
            return;
        }

        // 检查是否已有任务在运行，避免重复调度
        if (isJobScheduled(context)) {
            LogUtils.d(TAG, "JobScheduler任务已存在，跳过重复调度");
            return;
        }

        // 取消之前的任务
        jobScheduler.cancel(JOB_ID);

        JobInfo.Builder jobBuilder = new JobInfo.Builder(JOB_ID,
            new ComponentName(context, KeepAliveJobService.class))
            .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE)
            .setRequiresCharging(false)
            .setRequiresDeviceIdle(false)
            .setPersisted(true); // 设备重启后保持

        // 根据设备类型设置不同的调度策略
        boolean isXiaomi = RomUtils.isXiaomi();
        long intervalMs;
        long backoffMs;

        if (isXiaomi) {
            // 小米设备使用更长的间隔和退避时间
            intervalMs = 30 * 60 * 1000; // 30分钟
            backoffMs = 5 * 60 * 1000;   // 5分钟退避
            LogUtils.d(TAG, "检测到小米设备，使用保守调度策略");
        } else {
            // 其他设备正常间隔
            intervalMs = 15 * 60 * 1000; // 15分钟
            backoffMs = 2 * 60 * 1000;   // 2分钟退避
        }

        jobBuilder.setBackoffCriteria(backoffMs, JobInfo.BACKOFF_POLICY_EXPONENTIAL);

        // 根据Android版本设置不同的调度策略
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // Android 7.0+，使用周期性任务
            jobBuilder.setPeriodic(intervalMs);
        } else {
            // Android 5.0-6.0，使用最小延迟
            jobBuilder.setMinimumLatency(intervalMs);
        }

        JobInfo jobInfo = jobBuilder.build();

        int result = jobScheduler.schedule(jobInfo);
        String deviceType = isXiaomi ? "小米设备" : "其他设备";
        if (result == JobScheduler.RESULT_SUCCESS) {
            LogUtils.d(TAG, "JobScheduler任务调度成功 - " + deviceType + " (间隔: " + intervalMs + "ms)");
        } else {
            LogUtils.e(TAG, "JobScheduler任务调度失败 - " + deviceType);
        }
    }
    
    /**
     * 取消JobScheduler任务
     */
    public static void cancelJob(Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            return;
        }
        
        JobScheduler jobScheduler = (JobScheduler) context.getSystemService(JOB_SCHEDULER_SERVICE);
        if (jobScheduler != null) {
            jobScheduler.cancel(JOB_ID);
            LogUtils.d(TAG, "JobScheduler任务已取消");
        }
    }
    
    /**
     * 检查JobScheduler任务是否已调度
     */
    public static boolean isJobScheduled(Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            return false;
        }
        
        JobScheduler jobScheduler = (JobScheduler) context.getSystemService(JOB_SCHEDULER_SERVICE);
        if (jobScheduler == null) {
            return false;
        }
        
        for (JobInfo jobInfo : jobScheduler.getAllPendingJobs()) {
            if (jobInfo.getId() == JOB_ID) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取Job ID
     */
    public static int getJobId() {
        return JOB_ID;
    }

    /**
     * 获取设备和Job状态信息（用于调试）
     */
    public static String getJobStatusInfo(Context context) {
        StringBuilder info = new StringBuilder();
        info.append("=== JobScheduler状态信息 ===\n");
        info.append("设备制造商: ").append(Build.MANUFACTURER).append("\n");
        info.append("设备型号: ").append(Build.MODEL).append("\n");
        info.append("是否小米设备: ").append(RomUtils.isXiaomi()).append("\n");
        info.append("Android版本: ").append(Build.VERSION.RELEASE).append("\n");
        info.append("SDK版本: ").append(Build.VERSION.SDK_INT).append("\n");

        long lastExecution = PreferencesUtils.getLong(context, PREF_LAST_EXECUTION_TIME, 0);
        int executionCount = PreferencesUtils.getInt(context, PREF_EXECUTION_COUNT, 0);

        info.append("上次执行时间: ");
        if (lastExecution > 0) {
            info.append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                .format(new java.util.Date(lastExecution)));
        } else {
            info.append("从未执行");
        }
        info.append("\n");

        info.append("当前小时执行次数: ").append(executionCount).append("\n");
        info.append("Job是否已调度: ").append(isJobScheduled(context)).append("\n");

        if (RomUtils.isXiaomi()) {
            info.append("小米设备特殊配置:\n");
            info.append("  - 最小执行间隔: ").append(XIAOMI_MIN_EXECUTION_INTERVAL / 1000 / 60).append("分钟\n");
            info.append("  - 每小时最大执行次数: ").append(XIAOMI_MAX_EXECUTIONS_PER_HOUR).append("次\n");
            info.append("  - 调度间隔: 30分钟\n");
        }

        return info.toString();
    }

    /**
     * 手动打印Job状态信息（用于调试）
     */
    public static void logJobStatusInfo(Context context) {
        LogUtils.d(TAG, getJobStatusInfo(context));
    }

    /**
     * 重置Job执行统计（用于测试）
     */
    public static void resetJobExecutionStats(Context context) {
        PreferencesUtils.remove(context, PREF_LAST_EXECUTION_TIME);
        PreferencesUtils.remove(context, PREF_EXECUTION_COUNT);
        LogUtils.d(TAG, "Job执行统计已重置");
    }
}
