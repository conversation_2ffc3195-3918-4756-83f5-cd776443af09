package com.totwoo.totwoo.service;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.Manifest;
import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.pm.ServiceInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Message;
import android.os.PowerManager;
import android.text.TextUtils;
import android.view.View;
import android.widget.RemoteViews;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.RomUtils;
import com.blankj.utilcode.util.Utils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.huawei.hms.maps.model.LatLng;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.BuildConfig;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.data.AlarmCustomNotifyLogic;
import com.totwoo.totwoo.data.AlarmLogic;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DelayedOperationUtil;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.location.MyMapLocationClient;
import com.totwoo.totwoo.utils.location.MyMapLocationClientOption;

import java.util.ArrayList;
import java.util.HashMap;

import cn.jpush.android.api.JPushInterface;
import no.nordicsemi.android.support.v18.scanner.BluetoothLeScannerCompat;
import no.nordicsemi.android.support.v18.scanner.ScanCallback;
import no.nordicsemi.android.support.v18.scanner.ScanResult;

public class KeepAliveService extends Service implements SubscriberListener {

    private static final String TAG = "KeepAliveService";
    private static final long KEEP_ALIVE_INTERVAL = 50; //保活50s一次
    public static final int FOREGROUND_SERVICE_ID = 0x1024;
    private static final String CHANNEL_ID = "20180311";

    private final HashMap<String, Bitmap> mHeadCacheMap = new HashMap<>();

    private Handler handler;

    private boolean hasRegister = false;

    NotificationManagerCompat notificationManager;

    @Override
    public void onCreate() {
        Log.e(TAG, "onCreate");
        super.onCreate();
        notificationManager = NotificationManagerCompat.from(this);
        createNotificationChannel();


        if (!hasRegister) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                registerReceiver(mBTStateChangedReceiver, new IntentFilter(
                        BluetoothAdapter.ACTION_STATE_CHANGED), RECEIVER_NOT_EXPORTED);
            } else {
                registerReceiver(mBTStateChangedReceiver, new IntentFilter(
                        BluetoothAdapter.ACTION_STATE_CHANGED));
            }
        }
        InjectUtils.injectOnlyEvent(this);
        hasRegister = true;

        if (!TextUtils.equals(BuildConfig.FLAVOR, "googleplay")) {
            startKeepAlive();
        }

        interStartForegroundService();

        // 重置业务中的提醒闹钟
        restartNotify();
    }

    /**
     * 启动前台服务
     */
    private void interStartForegroundService() {
        Intent intent = new Intent(this, KeepAliveService.class);
        intent.setAction(BleParams.ACTION_REFRESH_FOREGROUND_STATUS);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O  && hasForegroundPermission(this)) {
            try {
                // targetSdk 31 以上, 后台开启前台服务可能会触发: ForegroundServiceStartNotAllowedException
                // https://developer.android.com/about/versions/12/behavior-changes-12#foreground-service-launch-restrictions
                startForegroundService(intent);
                startForegroundInner();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            startService(intent);
        }
    }

    private void createNotificationChannel() {
        if (notificationManager == null) {
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "TOTWOO_CONNECT",
                    NotificationManager.IMPORTANCE_HIGH
            );

            notificationManager.createNotificationChannel(serviceChannel);
        }
    }

    public Notification getJewelryStateNotification() {
        NotificationCompat.Builder builder = getStateNotificationBuilder(CHANNEL_ID);
        Intent intent = new Intent(this, HomeActivityControl.getInstance().getTagertClass());
        PendingIntent pendingIntent = PendingIntent.getActivity(this,
                33, intent,
                Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
        builder.setContentIntent(pendingIntent);
        return builder.build();
    }

    @NonNull
    private NotificationCompat.Builder getStateNotificationBuilder(String channelId) {
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, channelId);
        builder.setSilent(true)
                .setWhen(System.currentTimeMillis())
                .setCategory(Notification.CATEGORY_STATUS)
                .setOngoing(true)
                .setSmallIcon(R.drawable.ic_launcher_small);
        boolean connect = JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED;
        if (ToTwooApplication.owner == null || TextUtils.isEmpty(ToTwooApplication.owner.getPairedId())) {
            String jewName = PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
            builder.setLargeIcon(BitmapFactory.decodeResource(getResources(), TextUtils.isEmpty(jewName) ?
                            R.mipmap.ic_launcher : BleParams.getJewelryResourceId(jewName)))
                    .setContentTitle("totwoo")
                    .setContentText(getBleStatue());
        } else {
            RemoteViews rv = new RemoteViews(AppUtils.getAppPackageName(), R.layout.paired_notification_layout);

            rv.setTextViewText(R.id.paired_notification_me_name_tv, owner.getNickName());
            rv.setTextViewText(R.id.paired_notification_paired_name_tv, PreferencesUtils.getString(this, CoupleLogic.PAIRED_PERSON_NICK_NAME, ""));


            Bitmap myHead = mHeadCacheMap.get(BitmapHelper.checkRealPath(owner.getHeaderUrl()));
            if (myHead != null) {
                rv.setImageViewBitmap(R.id.paired_notification_me_head_iv, myHead);
            } else {
                Glide.with(this).asBitmap().load(BitmapHelper.checkRealPath(owner.getHeaderUrl())).apply(RequestOptions.bitmapTransform(new CircleCrop())).listener(new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                        mHeadCacheMap.put(BitmapHelper.checkRealPath(owner.getHeaderUrl()), resource);
                        rv.setImageViewBitmap(R.id.paired_notification_me_head_iv, resource);
                        return false;
                    }
                }).submit();
            }

            Bitmap otherHead = mHeadCacheMap.get(BitmapHelper.checkRealPath(PreferencesUtils.getString(this, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, "")));
            if (otherHead != null) {
                rv.setImageViewBitmap(R.id.paired_notification_paired_head_iv, otherHead);
            } else {
                Glide.with(this).asBitmap().load(BitmapHelper.checkRealPath(PreferencesUtils.getString(this, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, ""))).apply(RequestOptions.bitmapTransform(new CircleCrop())).listener(new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                        mHeadCacheMap.put(BitmapHelper.checkRealPath(PreferencesUtils.getString(ToTwooApplication.baseContext, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, "")), resource);
                        rv.setImageViewBitmap(R.id.paired_notification_paired_head_iv, resource);
                        return false;
                    }
                }).submit();
            }

            String jewInfoStr = getBleStatue();
            rv.setTextViewText(R.id.paired_notification_me_jewelry_state_tv, jewInfoStr);

            int resId = connect ? R.drawable.paired_notification_jew_connected_icon : R.drawable.paired_notification_jew_disconnected_icon;
            rv.setTextViewCompoundDrawables(R.id.paired_notification_me_name_tv, resId, 0, 0, 0);

            int twoCount = PreferencesUtils.getInt(this, CoupleLogic.PAIRED_TOTWOO_COUNT, -1);
            if (twoCount < 0) {
                rv.setViewVisibility(R.id.paired_notification_totwoo_count_tv, View.INVISIBLE);
            } else {
                rv.setViewVisibility(R.id.paired_notification_totwoo_count_tv, View.VISIBLE);
                rv.setTextViewText(R.id.paired_notification_totwoo_count_tv, getString(R.string.love_collect, String.valueOf(twoCount)));
            }
            builder.setStyle(new NotificationCompat.DecoratedCustomViewStyle()).setCustomContentView(rv).setCustomBigContentView(rv);
        }

        return builder;
    }

    private String getBleStatue() {
        if (ActivityUtils.getTopActivity() == null) {
            return "";
        }
        return switch (JewInfoSingleton.getInstance().getConnectState()) {
            case JewInfoSingleton.STATE_CONNECTED ->
                    ActivityUtils.getTopActivity().getString(R.string.jewelry_connected);
            case JewInfoSingleton.STATE_DISCONNECTED ->
                    ActivityUtils.getTopActivity().getString(R.string.jewelry_disconnected);
            case JewInfoSingleton.STATE_UNPAIRED ->
                    ActivityUtils.getTopActivity().getString(R.string.connecting_jewelry_);
            default -> ActivityUtils.getTopActivity().getString(R.string.totwoo_connecting);
        };
    }

    @EventInject(eventType = S.E.E_UPDATE_PAIRED_STATE)
    public void notifyTotwooState(EventData data) {
        LogUtils.d("JewelryConnectService totwoo data change.");
        foregroundNotificationUpdate();
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_STATUS_CHANGE)
    public void notifyJewelryState(EventData data) {
        //设备电量变化不做刷新
        if (data != null) {
            return;
        }

        LogUtils.d("JewelryConnectService paired state change: "
                + JewInfoSingleton.getInstance().getConnectState() + " >>>>: " + PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, ""));

        foregroundNotificationUpdate();
    }

    /**
     * 更新前台通知，2s 内只触发一次，防止频繁触发
     */
    private void foregroundNotificationUpdate() {
        DelayedOperationUtil.handleMessageReceived(2000, new Runnable() {
            @Override
            public void run() {
                //Android 12 或更高版本为目标平台的应用在后台运行时无法启动前台服务
                if (!AppUtils.isAppRunning(getPackageName())) {
                    return;
                }
                if (isScreenOn(ToTwooApplication.baseContext)) {
                    updateNotification();
                }
            }
        });
    }


    public synchronized void  updateNotification() {
        if (notificationManager == null) {
            return;
        }

        if (RomUtils.isXiaomi()) {
            notificationManager.cancel(FOREGROUND_SERVICE_ID);
        }

        Notification notification = getJewelryStateNotification();
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
            notificationManager.notify(FOREGROUND_SERVICE_ID, notification);
        }
    }

    public boolean isScreenOn(Context context) {
        PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        return powerManager.isInteractive();
    }

    @Override
    public void onDestroy() {
        try {
            unregisterReceiver(mBTStateChangedReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 停止所有保活机制
        stopAllKeepAliveMechanisms();

        if (notificationManager == null) {
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O  && hasForegroundPermission(this)) {
            stopForeground(true);
        }

        LogUtils.d(TAG, "KeepAliveService已销毁");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            Log.d(TAG, String.format("intent action: %s, data: %s, Extras: %s", intent.getAction(), intent.getData(), CommonUtils.bundleToString(intent.getExtras())));

            // 通知栏快捷回复按钮, 或者手表端的快捷回复
            if (TextUtils.equals(action, BleParams.ACTION_SEND_TOTWOO)) {
                BluetoothManage.getInstance().sendTotwoo(false);
            } else if (TextUtils.equals(action, BleParams.ACTION_KEEP_ALIVE)) {
                if (isBluetoothEnable() && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    BluetoothManage.getInstance().reconnect(false);
                }
            } else if (TextUtils.equals(action, BleParams.ACTION_RECONNECT)) {
                BluetoothManage.getInstance().reconnect(false);
            } else if (TextUtils.equals(action, BleParams.ACTION_START)) {
                handleServiceStart();
            } else if (TextUtils.equals(action, BleParams.ACTION_REFRESH_FOREGROUND_STATUS)) {
                // 刷新前台状态（保留原有逻辑）
            } else if (TextUtils.equals(action, BleParams.ACTION_BLE_SCAN_NOTIFY)) {
                dealBleScanNotify(intent);
            } else {
                // 统一处理保活相关的action
                handleKeepAliveAction(action, intent);
            }
        }

        if (NotifyUtil.getAppNotifyModel(ToTwooApplication.baseContext).isNotifySwitch()) {
            LogUtils.e("JewelryConnectService toggleNotificationListenerService");
            toggleNotificationListenerService();
            CommonUtils.isNotificationServiceEnabled();
        }
        return START_STICKY;
    }

    private void startForegroundInner() {
        try {
            Notification stateNotification = getJewelryStateNotification();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                try {
                    // targetSdk 31 以上, 后台开启前台服务可能会触发: ForegroundServiceStartNotAllowedException
                    // https://developer.android.com/about/versions/12/behavior-changes-12#foreground-service-launch-restrictions
                    startForeground(FOREGROUND_SERVICE_ID, stateNotification,
                            ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                startForeground(FOREGROUND_SERVICE_ID, stateNotification);
            }
            LogUtils.e("开启通知");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理后台BLE 扫描的处理结果
     *
     * @param intent
     */
    private void dealBleScanNotify(Intent intent) {
        //  one or more of the extras EXTRA_CALLBACK_TYPE, EXTRA_ERROR_CODE and EXTRA_LIST_SCAN_RESULT to indicate the result of the scan.
        int errorCode = intent.getIntExtra(BluetoothLeScannerCompat.EXTRA_ERROR_CODE, 0);
        ArrayList<ScanResult> results = intent.getParcelableArrayListExtra(BluetoothLeScannerCompat.EXTRA_LIST_SCAN_RESULT);

        if (errorCode == ScanCallback.SCAN_FAILED_ALREADY_STARTED) {
            BluetoothManage.getInstance().stopBackgroundScan();
            BluetoothManage.getInstance().startBackgroundScan();
        } else if (errorCode == ScanCallback.SCAN_FAILED_SCANNING_TOO_FREQUENTLY) {
            // 1 分钟后重试
            new Handler().postDelayed(() -> BluetoothManage.getInstance().startBackgroundScan(), 60 * 1000);
        } else if (errorCode != 0) {
            // 其他类型错误, 直接忽略, 不处理
            LogUtils.e("Start background ble scan failed: " + errorCode);
        }

        String address = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, null);
        String name = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, null);

        if (address == null || !BluetoothAdapter.checkBluetoothAddress(address) || TextUtils.isEmpty(name)) {
            // 如果当前未绑定首饰, 或者绑定首饰无效, 直接忽略本次结果, 且停止后台扫描
            BluetoothManage.getInstance().stopBackgroundScan();
            return;
        }

        boolean ok = false;
        if (results != null && results.size() > 0) {
            ScanResult res = results.get(0);
            if (res != null && res.getScanRecord() != null) {
                // 扫描结果不匹配, 证明扫描器对应的过滤器已经过时, 需要重新启动新的扫描任务
                if (!TextUtils.equals(res.getScanRecord().getDeviceName(), name) || !TextUtils.equals(res.getDevice().getAddress(), address)) {
                    BluetoothManage.getInstance().stopBackgroundScan();
                    BluetoothManage.getInstance().startBackgroundScan();
                } else {
                    // 连接指定设备
                    BluetoothManage.getInstance().connect(res);
                    ok = true;
                }
            }
            // 这里代表本地扫描结果无效, 直接忽略本次唤醒结果即可
        }

        // 如果当前没有结果, 则当做普通的唤醒, 尝试重连即可
        if (!ok) {
            BluetoothManage.getInstance().reconnect(false);
        }
        //更新去掉
//        startForegroundInner();
    }

    /**
     * 业务中的闹钟重新设置, 例如喝水提醒, 灯光提醒等
     */
    private void restartNotify() {
        HandlerThread handlerThread = new HandlerThread("keepAlive");
        handlerThread.start();
        handler = new Handler(handlerThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                if ( Utils.getApp()!=null) {
                    try {
                        AlarmLogic.getInstance().notifyAlarm();
                        AlarmCustomNotifyLogic.getInstance().notifyAlarm();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    handler.sendEmptyMessageDelayed(0, BleParams.NOTIFY_INTERVAL);
                    LogUtils.i("aab JPushInterface.isPushStopped(KeepAliveService.this) = " + JPushInterface.isPushStopped(KeepAliveService.this));
                }
            }
        };
        handler.sendEmptyMessage(0);
    }

    private boolean isInitService = false;

    private void toggleNotificationListenerService() {
        try {
            if (isInitService) {
                return;
            }
            isInitService = true;
            PackageManager pm = getPackageManager();

            LogUtils.e("JewelryConnectService toggleNotificationListenerService");
            pm.setComponentEnabledSetting(new ComponentName(this, AppNotifyRemindService.class),
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);

            pm.setComponentEnabledSetting(new ComponentName(this, AppNotifyRemindService.class),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
            startService(new Intent(KeepAliveService.this, AppNotifyRemindService.class));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 间隔开始执行 配对状态的常连检测
     */
    private void startKeepAlive() {
//        PeriodicWorkRequest keepAliveRequest = new PeriodicWorkRequest
//                .Builder(KeepAliveWorker.class, KEEP_ALIVE_INTERVAL, TimeUnit.MINUTES)
//                .setConstraints(new Constraints.Builder()
//                        .setRequiredNetworkType(NetworkType.NOT_REQUIRED) // 需要的网络类型
//                        .build())
//                .build();
//
//        WorkManager.getInstance(this).enqueueUniquePeriodicWork(
//                "KeepAliveWork",
//                ExistingPeriodicWorkPolicy.REPLACE, // 替换现有的任务
//                keepAliveRequest
//        );

        try {
            Intent i = new Intent(BleParams.ACTION_KEEP_ALIVE);
            i.setClass(this, KeepAliveService.class);
            PendingIntent pi = PendingIntent.getService(this, 0, i, Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
            AlarmManager alarmMgr = (AlarmManager) getSystemService(ALARM_SERVICE);

            // 智能AlarmManager策略
            long nextTriggerTime = System.currentTimeMillis() + KEEP_ALIVE_INTERVAL;
            String strategyInfo = getAlarmManagerStrategy(alarmMgr);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && alarmMgr.canScheduleExactAlarms()) {
                // Android 12+且有精确闹钟权限，使用精确闹钟
                alarmMgr.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, nextTriggerTime, pi);
                LogUtils.d(TAG, "保活策略: 精确闹钟 | 间隔: " + (KEEP_ALIVE_INTERVAL/1000) + "秒 | " + strategyInfo);
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Android 6+，使用setAndAllowWhileIdle
                alarmMgr.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, nextTriggerTime, pi);
                LogUtils.d(TAG, "保活策略: setAndAllowWhileIdle | 间隔: " + (KEEP_ALIVE_INTERVAL/1000) + "秒 | " + strategyInfo);
            } else {
                // Android 6以下，使用setRepeating
                alarmMgr.setRepeating(AlarmManager.RTC_WAKEUP, nextTriggerTime, KEEP_ALIVE_INTERVAL, pi);
                LogUtils.d(TAG, "保活策略: setRepeating | 间隔: " + (KEEP_ALIVE_INTERVAL/1000) + "秒 | " + strategyInfo);
            }
        } catch (Throwable e) {
            LogUtils.e(TAG, "启动保活机制失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 监控蓝牙开闭状态的广播接收器
     */
    private BroadcastReceiver mBTStateChangedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (!BleParams.isBluetoothJewelry(null)) {
                return;
            }
            if (intent != null && TextUtils.equals(intent.getAction(),BluetoothAdapter.ACTION_STATE_CHANGED)) {
                int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0);
                LogUtils.w("Bluetooth state change: " + state);
                if (state == BluetoothAdapter.STATE_ON) {
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                        BluetoothManage.getInstance().blueToothTurnOn(ToTwooApplication.baseContext);
                    }, 500);
                } else if (state == BluetoothAdapter.STATE_OFF) {
                    BluetoothManage.getInstance().blueToothTurnOff();
                    cancelReconnect();
                }
            }
        }
    };

    /**
     * 通过AlarmManager间隔时间, 执行重连的操作
     */
    public void scheduleReconnect() {
        if (!BleParams.isBluetoothJewelry(null)) {
            cancelReconnect();
            return;
        }
        // 上次重连的时间间隔
        int interval = PreferencesUtils.getInt(this, BleParams.PREF_RECHECK, BleParams.DEFAULT_RETRY_INTERVAL);
        int tempInterval = Math.min(interval + 2000, BleParams.MAXIMUM_RETRY_INTERVAL);
        // 保存最新的间隔
        PreferencesUtils.put(this, BleParams.PREF_RECHECK, tempInterval);


        if (AppUtils.isAppRunning(AppUtils.getAppPackageName())) {
            handler.postDelayed(() -> {
                BluetoothManage.getInstance().reconnect(false);
            }, tempInterval);
        } else {
            // 设置智能重连的 AlarmService
            Intent i = new Intent();
            i.setClass(this, KeepAliveService.class);
            i.setAction(BleParams.ACTION_RECONNECT);
            PendingIntent pi = PendingIntent.getService(this, 0, i, Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
            AlarmManager alarmMgr = (AlarmManager) getSystemService(ALARM_SERVICE);

            // 智能重连策略 - 优先使用精确闹钟
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && alarmMgr.canScheduleExactAlarms()) {
                // Android 12+且有精确闹钟权限，使用精确闹钟（最快重连）
                alarmMgr.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP,
                        System.currentTimeMillis() + tempInterval, pi);
                LogUtils.d(TAG, "使用精确闹钟重连，间隔: " + tempInterval + "ms");
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Android 6+，使用setAndAllowWhileIdle（可能有延迟）
                alarmMgr.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP,
                        System.currentTimeMillis() + tempInterval, pi);
                LogUtils.d(TAG, "使用setAndAllowWhileIdle重连，间隔: " + tempInterval + "ms");
            } else {
                // Android 6以下，使用set
                alarmMgr.set(AlarmManager.RTC_WAKEUP,
                        System.currentTimeMillis() + tempInterval, pi);
                LogUtils.d(TAG, "使用set重连，间隔: " + tempInterval + "ms");
            }
            com.tencent.mars.xlog.Log.e("BluetoothWrapper", "scheduleReconnect after " + tempInterval);
        }
    }

    /**
     * 取消重连的操作机制
     */
    public void cancelReconnect() {
        Intent i = new Intent();
        i.setClass(KeepAliveService.this, KeepAliveService.class);
        i.setAction(BleParams.ACTION_RECONNECT);
        PendingIntent pi = PendingIntent.getService(KeepAliveService.this,
                0, i, Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
        AlarmManager alarmMgr = (AlarmManager) getSystemService(ALARM_SERVICE);
        alarmMgr.cancel(pi);
    }

    /**
     * 当前用户蓝牙是否开启
     *
     * @return
     */
    public boolean isBluetoothEnable() {
        BluetoothManager bm = (BluetoothManager) this
                .getSystemService(Context.BLUETOOTH_SERVICE);
        if (bm == null) {
            return false;
        }

        BluetoothAdapter adapter = bm.getAdapter();
        if (adapter == null) {
            return false;
        }
        return adapter.isEnabled();
    }

    /**
     * 检查是否有精确闹钟权限
     */
    public boolean hasExactAlarmPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            AlarmManager alarmManager = (AlarmManager) getSystemService(ALARM_SERVICE);
            return alarmManager.canScheduleExactAlarms();
        }
        return true; // Android 12以下默认有权限
    }

    /**
     * 获取AlarmManager策略信息
     */
    private String getAlarmManagerStrategy(AlarmManager alarmMgr) {
        StringBuilder info = new StringBuilder();
        info.append("Android ").append(Build.VERSION.SDK_INT);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            info.append(" | 精确闹钟权限: ").append(alarmMgr.canScheduleExactAlarms() ? "已授权" : "未授权");
        }

        return info.toString();
    }

    /**
     * 获取保活状态信息
     */
    public String getKeepAliveStatus() {
        StringBuilder status = new StringBuilder();
        status.append("📊 保活状态信息:\n");
        status.append("- 前台服务: ").append(isServiceRunning() ? "✅ 运行中" : "❌ 未运行").append("\n");
        status.append("- 蓝牙状态: ").append(isBluetoothEnable() ? "✅ 已开启" : "❌ 未开启").append("\n");
        status.append("- 精确闹钟权限: ").append(hasExactAlarmPermission() ? "✅ 已授权" : "⚠️ 未授权").append("\n");

        // 添加保活机制状态
        status.append("- WorkManager: ").append(isWorkManagerActive() ? "✅ 活跃" : "❌ 未活跃").append("\n");
        status.append("- JobScheduler: ").append(isJobSchedulerActive() ? "✅ 活跃" : "❌ 未活跃").append("\n");
        status.append("- BLE扫描: ").append(isBleScanning() ? "✅ 活跃" : "❌ 未活跃").append("\n");

        if (!hasExactAlarmPermission()) {
            status.append("\n💡 建议授权精确闹钟权限以获得最佳保活效果");
        }

        return status.toString();
    }

    private boolean isServiceRunning() {
        return ToTwooApplication.mService != null;
    }

    /**
     * 处理服务启动
     */
    private void handleServiceStart() {
        LogUtils.d(TAG, "处理服务启动");
        if (ToTwooApplication.mService == null) {
            ToTwooApplication.mService = this;
        }
        // 启动所有保活机制
        startAllKeepAliveMechanisms();
    }

    /**
     * 统一处理保活相关的action
     */
    private void handleKeepAliveAction(String action, Intent intent) {
        switch (action) {
            case "WORKMANAGER_RESTART":
                LogUtils.d(TAG, "WorkManager触发服务重启");
                handleWorkManagerRestart();
                break;

            case "JOBSCHEDULER_RESTART":
                LogUtils.d(TAG, "JobScheduler触发服务重启");
                handleJobSchedulerRestart();
                break;

            case "SYSTEM_EVENT_TRIGGER":
                String trigger = intent.getStringExtra("trigger");
                LogUtils.d(TAG, "系统事件触发: " + trigger);
                handleSystemEventTrigger(trigger);
                break;

            case "SCREEN_LOCK_MODE":
                LogUtils.d(TAG, "进入屏幕锁定模式");
                handleScreenLockMode();
                break;

            case "BOOT_RESTORE":
                LogUtils.d(TAG, "设备重启后恢复");
                handleBootRestore();
                break;

            case "ADJUST_STRATEGY":
                String strategy = intent.getStringExtra("strategy");
                LogUtils.d(TAG, "调整保活策略: " + strategy);
                handleAdjustStrategy(strategy);
                break;

            default:
                LogUtils.w(TAG, "未知的保活action: " + action);
                break;
        }
    }

    /**
     * 处理WorkManager重启
     */
    private void handleWorkManagerRestart() {
        try {
            // 确保服务正常运行
            if (ToTwooApplication.mService == null) {
                ToTwooApplication.mService = this;
            }

            // 启动前台服务
            startForegroundInner();

            // 启动保活机制
            startKeepAlive();

            // 启动智能BLE扫描
            startIntelligentBleScanning();

            LogUtils.d(TAG, "WorkManager重启处理完成");
        } catch (Exception e) {
            LogUtils.e(TAG, "WorkManager重启处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理JobScheduler重启
     */
    private void handleJobSchedulerRestart() {
        try {
            // 确保服务正常运行
            if (ToTwooApplication.mService == null) {
                ToTwooApplication.mService = this;
            }

            // 启动前台服务
            startForegroundInner();

            // 检查蓝牙连接
            if (isBluetoothEnable() && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                BluetoothManage.getInstance().reconnect(false);
            }

            LogUtils.d(TAG, "JobScheduler重启处理完成");
        } catch (Exception e) {
            LogUtils.e(TAG, "JobScheduler重启处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理系统事件触发
     */
    private void handleSystemEventTrigger(String trigger) {
        try {
            LogUtils.d(TAG, "处理系统事件: " + trigger);

            // 确保前台服务运行
            startForegroundInner();

            // 检查蓝牙连接状态
            if (isBluetoothEnable()) {
                int state = JewInfoSingleton.getInstance().getConnectState();
                if (state != JewInfoSingleton.STATE_CONNECTED) {
                    LogUtils.d(TAG, "系统事件触发蓝牙重连");
                    BluetoothManage.getInstance().reconnect(false);
                }
            }

            // 重新启动智能BLE扫描
            startIntelligentBleScanning();

        } catch (Exception e) {
            LogUtils.e(TAG, "系统事件处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理屏幕锁定模式
     */
    private void handleScreenLockMode() {
        try {
            LogUtils.d(TAG, "进入屏幕锁定模式");

            // 强化前台服务
            startForegroundInner();

            // 启动智能BLE扫描
            startIntelligentBleScanning();

            // 调整扫描策略为省电模式
            com.totwoo.totwoo.utils.IntelligentBleScanner.getInstance(this)
                .adjustScanModeBasedOnBattery();

        } catch (Exception e) {
            LogUtils.e(TAG, "屏幕锁定模式处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理设备重启后恢复
     */
    private void handleBootRestore() {
        try {
            LogUtils.d(TAG, "设备重启后恢复保活");

            // 确保服务正常运行
            if (ToTwooApplication.mService == null) {
                ToTwooApplication.mService = this;
            }

            // 启动前台服务
            startForegroundInner();

            // 启动所有保活机制
            startAllKeepAliveMechanisms();

        } catch (Exception e) {
            LogUtils.e(TAG, "设备重启后恢复失败: " + e.getMessage());
        }
    }

    /**
     * 处理策略调整
     */
    private void handleAdjustStrategy(String strategy) {
        try {
            LogUtils.d(TAG, "调整保活策略: " + strategy);

            com.totwoo.totwoo.utils.IntelligentBleScanner scanner =
                com.totwoo.totwoo.utils.IntelligentBleScanner.getInstance(this);

            switch (strategy) {
                case "NORMAL":
                    scanner.setScanMode(com.totwoo.totwoo.utils.IntelligentBleScanner.ScanMode.NORMAL);
                    break;
                case "POWER_SAVING":
                    scanner.setScanMode(com.totwoo.totwoo.utils.IntelligentBleScanner.ScanMode.POWER_SAVING);
                    break;
                case "ULTRA_SAVING":
                    scanner.setScanMode(com.totwoo.totwoo.utils.IntelligentBleScanner.ScanMode.ULTRA_SAVING);
                    break;
                default:
                    scanner.adjustScanModeBasedOnBattery();
                    break;
            }

        } catch (Exception e) {
            LogUtils.e(TAG, "策略调整失败: " + e.getMessage());
        }
    }

    /**
     * 启动所有保活机制
     */
    private void startAllKeepAliveMechanisms() {
        try {
            LogUtils.d(TAG, "启动所有保活机制");

            // 1. 启动AlarmManager保活
            startKeepAlive();

            // 2. 启动WorkManager
            com.totwoo.totwoo.worker.BluetoothKeepAliveWorker.scheduleWork(this);

            // 3. 启动JobScheduler
            com.totwoo.totwoo.service.KeepAliveJobService.scheduleJob(this);

            // 4. 启动智能BLE扫描
            startIntelligentBleScanning();

            // 5. 注册系统事件监听
            com.totwoo.totwoo.receiver.KeepAliveSystemReceiver.registerSystemReceiver(this);

            LogUtils.d(TAG, "所有保活机制启动完成");

        } catch (Exception e) {
            LogUtils.e(TAG, "启动保活机制失败: " + e.getMessage());
        }
    }

    /**
     * 启动智能BLE扫描
     */
    private void startIntelligentBleScanning() {
        try {
            // 检查是否有配对的蓝牙设备
            String jewelryName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
            if (TextUtils.isEmpty(jewelryName) || !BleParams.isBluetoothJewelry(jewelryName)) {
                LogUtils.d(TAG, "没有配对的蓝牙首饰，跳过BLE扫描");
                return;
            }

            com.totwoo.totwoo.utils.IntelligentBleScanner.getInstance(this)
                .startIntelligentScanning();

            LogUtils.d(TAG, "智能BLE扫描已启动");

        } catch (Exception e) {
            LogUtils.e(TAG, "启动智能BLE扫描失败: " + e.getMessage());
        }
    }

    /**
     * 停止所有保活机制
     */
    private void stopAllKeepAliveMechanisms() {
        try {
            LogUtils.d(TAG, "停止所有保活机制");

            // 1. 停止AlarmManager
            cancelKeepAliveAlarm();

            // 2. 停止WorkManager
            com.totwoo.totwoo.worker.BluetoothKeepAliveWorker.cancelWork(this);

            // 3. 停止JobScheduler
            com.totwoo.totwoo.service.KeepAliveJobService.cancelJob(this);

            // 4. 停止智能BLE扫描
            com.totwoo.totwoo.utils.IntelligentBleScanner.getInstance(this)
                .stopIntelligentScanning();

            LogUtils.d(TAG, "所有保活机制已停止");

        } catch (Exception e) {
            LogUtils.e(TAG, "停止保活机制失败: " + e.getMessage());
        }
    }

    /**
     * 取消保活AlarmManager
     */
    private void cancelKeepAliveAlarm() {
        try {
            Intent i = new Intent(BleParams.ACTION_KEEP_ALIVE);
            i.setClass(this, KeepAliveService.class);
            PendingIntent pi = PendingIntent.getService(this, 0, i, Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
            AlarmManager alarmMgr = (AlarmManager) getSystemService(ALARM_SERVICE);
            alarmMgr.cancel(pi);
            LogUtils.d(TAG, "保活AlarmManager已取消");
        } catch (Exception e) {
            LogUtils.e(TAG, "取消保活AlarmManager失败: " + e.getMessage());
        }
    }

    /**
     * 检查WorkManager是否活跃
     */
    private boolean isWorkManagerActive() {
        try {
            return androidx.work.WorkManager.getInstance(this)
                .getWorkInfosForUniqueWork(com.totwoo.totwoo.worker.BluetoothKeepAliveWorker.getWorkName())
                .get().size() > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查JobScheduler是否活跃
     */
    private boolean isJobSchedulerActive() {
        return com.totwoo.totwoo.service.KeepAliveJobService.isJobScheduled(this);
    }

    /**
     * 检查BLE扫描是否活跃
     */
    private boolean isBleScanning() {
        try {
            return com.totwoo.totwoo.utils.IntelligentBleScanner.getInstance(this).isScanningActive();
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isLocate;

    private LatLng myLocationLatLng;
    //声明mlocationClient对象
    public MyMapLocationClient mlocationClient;
    //声明mLocationOption对象
    public MyMapLocationClientOption mLocationOption = null;

    private long time;
    private String guard_id;

    public void startLocation(long restTime, String guard_id) {
        if (isLocate) {
            return;
        }
        try {
            this.time = restTime;
            this.guard_id = guard_id;
            if (mlocationClient == null) {
                mlocationClient = MyMapLocationClient.newLocationClient(this);

                //初始化定位参数
                mLocationOption = new MyMapLocationClientOption();
                //设置定位监听
                mlocationClient.setLocationListener(aMapLocation -> {
                    LogUtils.e("aab aMapLocation get");
                    if (aMapLocation.getErrorCode() == 0) {
                        myLocationLatLng = new LatLng(aMapLocation.getLatitude(), aMapLocation.getLongitude());
                    }
                });
                //设置定位模式为高精度模式，Battery_Saving为低功耗模式，Device_Sensors是仅设备模式
                mLocationOption.setLocationMode(MyMapLocationClientOption.MyMapLocationMode.PRIORITY_HIGH_ACCURACY);
                //设置定位间隔,单位毫秒,默认为2000ms
                mLocationOption.setInterval(30000);
                //设置定位参数
                mlocationClient.setLocationOption(mLocationOption);
            }
            mlocationClient.startLocation();
            isLocate = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private MyBinder mBinder = new MyBinder();

    public class MyBinder extends Binder {
        public KeepAliveService getService() {
            return KeepAliveService.this;
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return mBinder;
    }


    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    public void endLocation() {
        if (mlocationClient != null) {
            mlocationClient.stopLocation();
        }
        isLocate = false;
    }


    public final boolean hasForegroundPermission(Context context) {
        if (Build.VERSION.SDK_INT < 34 || ContextCompat.checkSelfPermission(context, "android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE") != PackageManager.PERMISSION_DENIED) {
            return true;
        }
        return false;
    }

}
