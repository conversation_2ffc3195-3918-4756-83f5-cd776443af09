package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.ToTwooApplication.baseContext;
import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.os.LocaleListCompat;

import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.UriUtils;
import com.blankj.utilcode.util.Utils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.BuildConfig;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.JewelryOTATestActivity;
import com.totwoo.totwoo.activity.LanguageSettingActivity;
import com.totwoo.totwoo.activity.MeSettingActivity;
import com.totwoo.totwoo.activity.MessageActivity;
import com.totwoo.totwoo.activity.ReminderSettingsActivity;
import com.totwoo.totwoo.activity.SetPermissionActivity;
import com.totwoo.totwoo.activity.WebViewActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.activity.security.SafeJewSettingActivity;
import com.totwoo.totwoo.bean.NotifyMessage;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.PictureSelectUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomBottomDialog;
import com.totwoo.totwoo.widget.RoundImageView;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class MeFragment extends BaseFragment implements SubscriberListener {
    public static final String ARGUMENT_TAG_BLACK_THEME = "argument_tag_black_theme";
    private static final int RESULT_CODE_MODIFY = 0x33;
    private static final int RESULT_CODE_CHANGE_LANGUAGE = 0x44;

    @BindView(R.id.setting_head_icon)
    RoundImageView mSettingHeadIcon;
    @BindView(R.id.setting_nick_name_tv)
    TextView mSettingNickNameTv;
    @BindView(R.id.user_item_message_center_tv)
    TextView mUserItemMessageCenterTv;
    @BindView(R.id.user_item_jewelry_manage_tv)
    TextView mJewelryTv;
    @BindView(R.id.user_app_version_tv)
    TextView mAppVersionTv;
    @BindView(R.id.user_item_jewelry_store_tv)
    TextView mStoreTv;
    @BindView(R.id.user_item_pay)
    FrameLayout mPayLayout;
    @BindView(R.id.me_item_feedback)
    FrameLayout mFeedBack;

    @BindView(R.id.user_item_language_setting)
    FrameLayout languageSetting;


    @BindView(R.id.fragment_me_notify_layout)
    View mNotifyLayout;
    @BindView(R.id.me_item_disclaimer)
    FrameLayout mDisclaimerLayout;


    CustomBottomDialog headIconDialog;

    /**
     * 是否黑色主题
     */
    private boolean isBlackTheme;

    private int testCount = 0;
    private long lastTestTime = 0;

    @SuppressLint("ClickableViewAccessibility")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
//        if (getArguments() != null) {
//            isBlackTheme = getArguments().getBoolean(ARGUMENT_TAG_BLACK_THEME, false);
//        }
        isBlackTheme = BleParams.isNfcJewelry(null);
        View view = inflater.inflate(isBlackTheme ? R.layout.fragment_me_black : R.layout.fragment_me, container, false);
        EdgeToEdgeUtils.setupBottomInsetsForLoveHomeAct(view);

        ButterKnife.bind(this, view);
        InjectUtils.injectOnlyEvent(this);

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        updateUserInfo();

        handleStore();
//        if (!Apputils.systemLanguageIsChinese(baseContext)) {
//            mFeedBack.setVisibility(View.GONE);
//        } else {
//            mFeedBack.setVisibility(View.VISIBLE);
//        }
        if (BleParams.isSecurityJewlery()) {
            mDisclaimerLayout.setVisibility(View.VISIBLE);
        }

        mAppVersionTv.setText("V " + Apputils.getVersionName(baseContext, true));


        mAppVersionTv.setOnLongClickListener(v -> {
            if (BuildConfig.DEBUG) {
                startActivity(new Intent(getActivity(), JewelryOTATestActivity.class));
            }
            return true;
        });

        return view;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case RESULT_CODE_MODIFY:
                updateUserInfo();
                break;
            case RESULT_CODE_CHANGE_LANGUAGE:
                if (resultCode == Activity.RESULT_OK && data.getStringExtra(LanguageSettingActivity.EXTRA_LANGUAGE_CHANGE) != null) {
                    LocaleListCompat appLocale = LocaleListCompat.forLanguageTags(data.getStringExtra(LanguageSettingActivity.EXTRA_LANGUAGE_CHANGE)); // 确保英语为回退语言
                    // Call this on the main thread as it may require Activity.restart()
                    AppCompatDelegate.setApplicationLocales(appLocale);
                    //重启后 homebase  有调用
//                    JewelryOnlineDataManager.getInstance().resetConnectedJewInfo();

                    try {
                        mSettingHeadIcon.postDelayed(() -> {
                            TimInitBusiness.refreshPermissionRequestContent(mSettingHeadIcon.getContext());
                        }, 2000);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @OnClick({R.id.fragment_me_notify_layout, R.id.fragment_me_setting_layout, R.id.setting_head_icon, R.id.setting_nick_name_tv, R.id.me_item_policy, R.id.user_item_language_setting,
            R.id.user_item_jewelry_manage, R.id.user_item_pay, R.id.user_item_message_center, R.id.user_item_help_center, R.id.me_item_feedback, R.id.me_item_terms_of_service,
            R.id.user_item_jewelry_store, R.id.user_item_app_version, R.id.me_content_view, R.id.me_item_disclaimer, R.id.edit_head, R.id.me_item_love_stories,
    })
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.edit_head:
                showHeadIconDialog();
                break;
            case R.id.fragment_me_notify_layout:
                if (BleParams.isSecurityJewlery()) {
                    startActivity(new Intent(getContext(), SafeJewSettingActivity.class));
                    return;
                }
                MobclickAgent.onEvent(baseContext, TrackEvent.ME_JEWLERY_SET_CLICK);
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
                    ToastUtils.show(getContext(), R.string.error_jewelry_connect, Toast.LENGTH_LONG);
                } else {
//                    if (BleParams.isMWJewlery() || BleParams.isCtJewlery()) {
                        startActivity(new Intent(getActivity(), ReminderSettingsActivity.class));
//                    } else {
//                        startActivity(new Intent(getActivity(), NotifyActivity.class));
//                    }
                }
                break;
            case R.id.fragment_me_setting_layout:
                startActivity(new Intent(getActivity(), SetPermissionActivity.class));
                MobclickAgent.onEvent(baseContext, TrackEvent.ME_MOBILE_SET_CLICK);
                break;
            case R.id.setting_head_icon:
            case R.id.setting_nick_name_tv:
            case R.id.me_content_view:
                startActivityForResult(new Intent(getActivity(), MeSettingActivity.class), RESULT_CODE_MODIFY);
                break;
            case R.id.user_item_jewelry_manage:
                MobclickAgent.onEvent(baseContext, TrackEvent.ME_JEWLERY_LIST_CLICK);
                CommonUtils.jumpToJewList(getActivity());
                break;
            case R.id.user_item_message_center:
                startActivity(new Intent(getActivity(), MessageActivity.class));
                break;
            case R.id.user_item_help_center:
                MobclickAgent.onEvent(baseContext, TrackEvent.ME_HELP_CLICK);
                if (BleParams.isSecurityJewlery()) {
                    WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_SAFE_INFO), false, false, getString(R.string.help), "");
                } else if (BleParams.isNfcJewelry(null)) {
                    WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_NFC_HELP), false, false, getString(R.string.help), "");
                } else {
                    WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_HELP), false, false, getString(R.string.help), "");
                }
                break;
            case R.id.me_item_feedback:
                MobclickAgent.onEvent(baseContext, TrackEvent.ME_FEEDBACK_CLICK);

                if (Apputils.systemLanguageIsChinese(getContext())) {
                    WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_CONTACT), false);
                } else {
                    WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_DIRECT_CUSTOMER_SERVICE), false);
                }

//                if (Apputils.systemLanguageIsChinese(getActivity())) {
////                    startActivity(new Intent(getActivity(), FeedbackQRCodeActivity.class));
//                } else {
//                    startActivity(new Intent(getActivity(), OpinionFeedbackActivity.class));
//                }
                break;
            case R.id.me_item_love_stories:
                MobclickAgent.onEvent(baseContext, TrackEvent.ME_LOVE_STORIES_CLICK);
                WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_LOVE_STORIES), false, false, getString(R.string.lovestories), "");
                break;
            case R.id.user_item_jewelry_store:
                String jewName = PreferencesUtils.getString(baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
                if (TextUtils.isEmpty(jewName)) {
                    WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_ABOUT), false);
                } else {
                    WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_STORE), false, false, getString(R.string.totwoo_store), "");
                }
                break;
            case R.id.user_item_pay:
                String url = "http://testapi.totwoo.com/safes/pay";
                url = url + "?imei=" + PreferencesUtils.getString(baseContext, BleParams.SAFE_JEWLERY_IMEI, "");
                url = url + "&totwoo_id=" + ToTwooApplication.owner.getTotwooId();
                int isChinese = Apputils.systemLanguageIsChinese(baseContext) ? 1 : 0;
                url = url + "&cn=" + isChinese;
                WebViewActivity.loadUrl(getActivity(), url, false);
                break;
            case R.id.user_item_app_version:
                if (getActivity() instanceof HomeBaseActivity) {
//                    ((HomeBaseActivity) getActivity()).checkAppUpdate(true);
                }

                // 连击 6 次
                if (SystemClock.elapsedRealtime() - lastTestTime < 1000) {
                    if (++testCount == 5 && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (!CommonUtils.isIgnoringBatteryOptimizations(requireContext())) {
                            CommonUtils.requestIgnoreBatteryOptimizations(getActivity());
                        } else {
                            ToastUtils.showLong(requireContext(), "Has allowed!");
                        }
                    }
                } else {
                    testCount = 0;
                }
                lastTestTime = SystemClock.elapsedRealtime();

                break;
            case R.id.me_item_policy:
//                startActivity(new Intent(getActivity(), TestColorActivity.class));
//                startActivity(new Intent(getActivity(), TestSleepActivity.class));
//                startActivity(new Intent(getActivity(), TestTogetherTimeActivity.class));
//                WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_PRIVATE_POLICY), false);
                WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_PRIVATE), false);
                break;
            case R.id.me_item_terms_of_service:
                WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_PRIVATE_POLICY), false);
                break;
            case R.id.me_item_disclaimer:
                WebViewActivity.loadUrl(getActivity(), HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_DISCLAIMER), false);
                break;
            case R.id.user_item_language_setting:
                startActivityForResult(new Intent(getActivity(), LanguageSettingActivity.class), RESULT_CODE_CHANGE_LANGUAGE);
                break;
//            case R.id.user_item_connect_permission:
//                startActivity(new Intent(getActivity(), SetPermissionActivity.class));
//                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void receiveNewMessage(NotifyMessage mesage) {
        if (mesage.isNew()) {
            mUserItemMessageCenterTv.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    isBlackTheme ? R.drawable.message_icon_white : R.drawable.message_icon, 0, R.drawable.red_point_icon, 0);
        } else {
            mUserItemMessageCenterTv.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    isBlackTheme ? R.drawable.message_icon_white : R.drawable.message_icon, 0, 0, 0);
        }
    }


    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_STATUS_CHANGE)
    public void notifyJewelryState(EventData data) {
        //设备电量变化不做刷新
        if (data != null) {
            return;
        }
        handleStore();
    }

    private void handleStore() {
        String jewName = PreferencesUtils.getString(baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        if (TextUtils.isEmpty(jewName)) {
            mJewelryTv.setText(R.string.my_jewelry);
//            mNotifyLayout.setVisibility(View.GONE);
            mStoreTv.setText(R.string.totwoo_about);
            mStoreTv.setCompoundDrawablesRelativeWithIntrinsicBounds(getResources().getDrawable(R.drawable.icon_understand), null, null, null);
        } else {
            mJewelryTv.setText(R.string.set_jew);
//            mNotifyLayout.setVisibility(View.VISIBLE);
            mStoreTv.setText(R.string.totwoo_store);
            mStoreTv.setCompoundDrawablesRelativeWithIntrinsicBounds(getResources().getDrawable(R.drawable.store_icon), null, null, null);
        }
    }


    private void updateUserInfo() {
        mSettingNickNameTv.setText(ToTwooApplication.owner.getNickName());
        RequestOptions options;
        if (ToTwooApplication.owner.getGender() == 0) {
            options = new RequestOptions()
                    .error(R.drawable.default_head_yellow);
        } else {
            options = new RequestOptions()
                    .error(R.drawable.default_head_yellow);
        }
        Glide.with(this).load(BitmapHelper.checkRealPath(ToTwooApplication.owner.getHeaderUrl())).apply(options).into(mSettingHeadIcon);
    }

    @Override
    public void onShow() {
        MobclickAgent.onPageStart(TrackEvent.ME_PAGE);
        MobclickAgent.onEvent(baseContext, TrackEvent.HOMEPAGE_BOTTOM_ME);

        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        if (mPayLayout != null) {
            if (LocalJewelryDBHelper.getInstance().hasSecurityJewelry()) {
                mPayLayout.setVisibility(View.VISIBLE);
            } else {
                mPayLayout.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }


    //新增修改头像功能
    private void showHeadIconDialog() {
        if (headIconDialog == null) {
            headIconDialog = new CustomBottomDialog(requireContext());
        }
        headIconDialog.setTitle(R.string.modify_head);
        LinearLayout picLayout = (LinearLayout) View.inflate(requireContext(), R.layout.dialog_inside_pic, null);

        headIconDialog.setMainView(picLayout);
        TextView mAlbum = picLayout.findViewById(R.id.pic_album_tv);
        TextView mCamera = picLayout.findViewById(R.id.pic_camera_tv);
        mAlbum.setOnClickListener(v -> {
            PictureSelectUtil.with((AppCompatActivity) getActivity()).gallery().crop().shapeOval(true).setCallback(uri -> {
                receivePhoto(uri);
                headIconDialog.dismiss();
            }).select();
        });
        // 相机tv监听点击开启拍照app
        mCamera.setOnClickListener(v -> {
            PictureSelectUtil.with((AppCompatActivity) getActivity()).camera().crop().shapeOval(true).setCallback(uri -> {
                receivePhoto(uri);
                headIconDialog.dismiss();
            }).select();
        });
        headIconDialog.show();
    }

    //增加图片压缩功能
    private void receivePhoto(Uri uri) {
        int maxWidth = ConvertUtils.dp2px(83);
        Bitmap bitmap = ImageUtils.compressBySampleSize(ImageUtils.getBitmap(UriUtils.uri2File(uri)),
                maxWidth, maxWidth, true);
        Glide.with(this).load(bitmap).into(mSettingHeadIcon);
        Log.e("MeFragment", "开始上报");
        postUserHeadPortrait(ConvertUtils.bitmap2Bytes(bitmap));
    }

    // 把bitmap转成file上传服务器
    public void postUserHeadPortrait(byte[] fileBytes) {
        // 上传图片
        HttpHelper.card.getQiNiuToken(1, "headimg").subscribeOn(Schedulers.io()).subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {
                ToastUtils.showLong(Utils.getApp(), R.string.upload_filed);
            }

            @Override
            public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                Log.e("MeFragment:token", getQiNiuTokenHttpBaseBean.toString());

//                byte[] fileBytes = CommonUtils.getFileBytesFromUri(MeSettingActivity.this, bitmap);
                if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0 && fileBytes != null) {
                    RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), fileBytes);
                    MultipartBody.Part part = MultipartBody.Part.createFormData("file", "head_icon", requestFile);
                    HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Subscriber<QiNiuResponse>() {
                                @Override
                                public void onCompleted() {

                                }

                                @Override
                                public void onError(Throwable e) {
                                    ToastUtils.showLong(Utils.getApp(), R.string.upload_filed);
                                }

                                @Override
                                public void onNext(QiNiuResponse qiNiuResponse) {
                                    Log.e("MeFragment:token", qiNiuResponse.toString());

                                    // 上传成功
                                    owner.setHeaderUrl(qiNiuResponse.getKey());
                                    synchronousUserInfo(true, true);
                                    ToastUtils.showLong(Utils.getApp(), R.string.upload_success);
                                    LogUtils.i("upload image success!" + qiNiuResponse.getKey());
                                }
                            });
                }
            }
        });
    }

    /**
     * 同步本地用户信息到服务器
     */
    private void synchronousUserInfo(boolean manual, boolean modifyHead) {
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("sex", owner.getGender());
        String love = null;
        switch (owner.getLoveStatus()) {
            case 0:
                love = "SEC";
                break;
            case 1:
                love = "S";
                break;
            case 2:
                love = "F";
                break;
            case 3:
                love = "M";
                break;
        }

        params.addFormDataPart("love_status", love);
        if (!owner.getCity().equals("")) {
            params.addFormDataPart("city", owner.getCity());
        }
        params.addFormDataPart("height", owner.getHeight() + "");
        params.addFormDataPart("weight", owner.getWeight() + "");
        params.addFormDataPart("birthday", owner.getBirthday());
        if (modifyHead) {
            params.addFormDataPart("head_portrait", owner.getHeaderUrl());
        }
        params.addFormDataPart("nick_name", owner.getNickName());

        Log.e("UpdateUserinfo", params.toString());
        HttpRequest.post(HttpHelper.URL_UPDATE_USER_INFO, params,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        Log.e("UpdateUserinfo：", s);
                        super.onLogicSuccess(s);
                        if (manual) {
                            ToastUtils.showLong(Utils.getApp(), R.string.modify_success);
                        }

                        // 更新缓存用户信息
                        PreferencesUtils.put(Utils.getApp(), CommonArgs.PREF_LAST_HEAD_ICON, owner.getHeaderUrl());
                        PreferencesUtils.put(Utils.getApp(), CommonArgs.PREF_LAST_PHONE, owner.getPhone());
                        PreferencesUtils.put(Utils.getApp(), CommonArgs.PREF_LAST_USERNAME, owner.getNickName());
                        PreferencesUtils.put(Utils.getApp(), CommonArgs.PREF_LAST_GENDER, owner.getGender());

                        try {
                            String head = new JSONObject(s).optString("head_portrait");
                            if (!TextUtils.isEmpty("head") && !head.equals(owner.getHeaderUrl())) {
                                owner.setHeaderUrl(head);
                                showHeadIcon();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }

                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {
                        switch (t.getErrorCode()) {
                            case 801:
                                ToastUtils.show(Utils.getApp(),
                                        R.string.nick_name_there, 2000);
                                break;
                        }
                    }
                });
    }

    private void showHeadIcon() {
        RequestOptions options;
        if (ToTwooApplication.owner.getGender() == 0) {
            options = new RequestOptions()
                    .error(R.drawable.default_head_yellow);
        } else {
            options = new RequestOptions()
                    .error(R.drawable.default_head_yellow);
        }
        Glide.with(this).load(BitmapHelper.checkRealPath(owner.getHeaderUrl())).apply(options).into(mSettingHeadIcon);
    }

}
