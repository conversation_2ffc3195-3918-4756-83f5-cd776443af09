package com.totwoo.totwoo.worker;

import android.content.Context;
import android.content.Intent;
import android.os.BatteryManager;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.work.BackoffPolicy;
import androidx.work.Constraints;
import androidx.work.ExistingPeriodicWorkPolicy;
import androidx.work.NetworkType;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.service.KeepAliveService;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.concurrent.TimeUnit;

/**
 * 蓝牙保活WorkManager
 * 
 * 功能：
 * 1. 15分钟周期检查服务状态
 * 2. 检查蓝牙连接状态
 * 3. 根据电池状态调整策略
 * 4. 异常自动恢复
 */
public class BluetoothKeepAliveWorker extends Worker {
    
    private static final String TAG = "BluetoothKeepAliveWorker";
    private static final String WORK_NAME = "bluetooth_keep_alive_work";
    private static final String WORK_TAG = "bluetooth_keep_alive";
    
    public BluetoothKeepAliveWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
    }
    
    @NonNull
    @Override
    public Result doWork() {
        long startTime = System.currentTimeMillis();
        LogUtils.d(TAG, "🔍 WorkManager保活检查开始");

        try {
            Context context = getApplicationContext();

            // 1. 检查是否有配对的蓝牙设备
            if (!hasBluetoothJewelry(context)) {
                LogUtils.d(TAG, "没有配对的蓝牙首饰，跳过保活检查");
                return Result.success();
            }

            // 2. 检查KeepAliveService状态
            boolean serviceRestarted = checkAndRestartKeepAliveService(context);

            // 3. 检查蓝牙连接状态
            boolean bluetoothReconnected = checkBluetoothConnection(context);

            // 4. 根据电池状态调整策略
            String batteryStrategy = adjustStrategyBasedOnBattery(context);

            // 5. 检查后台扫描状态
            checkBackgroundScanning(context);

            // 生成检查报告
            long duration = System.currentTimeMillis() - startTime;
            String report = generateCheckReport(serviceRestarted, bluetoothReconnected, batteryStrategy, duration);
            LogUtils.d(TAG, "✅ WorkManager保活检查完成\n" + report);

            return Result.success();
            
        } catch (Exception e) {
            LogUtils.e(TAG, "WorkManager保活检查失败: " + e.getMessage());
            return Result.retry(); // 失败时重试
        }
    }
    
    /**
     * 检查是否有蓝牙首饰
     */
    private boolean hasBluetoothJewelry(Context context) {
        String jewelryName = PreferencesUtils.getString(context, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        return !TextUtils.isEmpty(jewelryName) && BleParams.isBluetoothJewelry(jewelryName);
    }
    
    /**
     * 检查并重启KeepAliveService
     */
    private boolean checkAndRestartKeepAliveService(Context context) {
        boolean isServiceRunning = ToTwooApplication.mService != null;
        LogUtils.d(TAG, "KeepAliveService状态: " + (isServiceRunning ? "✅ 运行中" : "❌ 未运行"));

        if (!isServiceRunning) {
            LogUtils.w(TAG, "KeepAliveService未运行，尝试重启");
            try {
                Intent serviceIntent = new Intent(context, KeepAliveService.class);
                serviceIntent.setAction("WORKMANAGER_RESTART");

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(serviceIntent);
                } else {
                    context.startService(serviceIntent);
                }

                LogUtils.d(TAG, "KeepAliveService重启成功");
                return true;
            } catch (Exception e) {
                LogUtils.e(TAG, "KeepAliveService重启失败: " + e.getMessage());
                return false;
            }
        }
        return false;
    }
    
    /**
     * 检查蓝牙连接状态
     */
    private boolean checkBluetoothConnection(Context context) {
        int bluetoothState = JewInfoSingleton.getInstance().getConnectState();
        String stateDesc = getBluetoothStateDescription(bluetoothState);
        LogUtils.d(TAG, "蓝牙连接状态: " + stateDesc);

        if (bluetoothState == JewInfoSingleton.STATE_DISCONNECTED ||
            bluetoothState == JewInfoSingleton.STATE_UNPAIRED) {
            LogUtils.w(TAG, "蓝牙未连接，触发重连");
            try {
                BluetoothManage.getInstance().reconnect(false);
                return true;
            } catch (Exception e) {
                LogUtils.e(TAG, "蓝牙重连失败: " + e.getMessage());
                return false;
            }
        }
        return false;
    }
    
    /**
     * 根据电池状态调整策略
     */
    private String adjustStrategyBasedOnBattery(Context context) {
        BatteryManager batteryManager = (BatteryManager) context.getSystemService(Context.BATTERY_SERVICE);
        if (batteryManager == null) return "电池管理器不可用";

        int batteryLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
        boolean isCharging = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_STATUS)
            == BatteryManager.BATTERY_STATUS_CHARGING;

        String batteryInfo = batteryLevel + "%" + (isCharging ? " (充电中)" : "");
        LogUtils.d(TAG, "电池状态: " + batteryInfo);

        String strategy;
        if (batteryLevel < 15 && !isCharging) {
            // 电池极低，进入省电模式
            strategy = "ULTRA_SAVING";
            LogUtils.d(TAG, "电池极低，进入极省电模式");
            notifyServiceAdjustStrategy(context, strategy);
        } else if (batteryLevel < 30 && !isCharging) {
            // 电池较低，适度省电
            strategy = "POWER_SAVING";
            LogUtils.d(TAG, "电池较低，进入省电模式");
            notifyServiceAdjustStrategy(context, strategy);
        } else {
            // 电池充足，正常模式
            strategy = "NORMAL";
            LogUtils.d(TAG, "电池充足，正常模式");
            notifyServiceAdjustStrategy(context, strategy);
        }

        return strategy + " (" + batteryInfo + ")";
    }
    
    /**
     * 检查后台扫描状态
     */
    private void checkBackgroundScanning(Context context) {
        try {
            // 启动智能BLE扫描
            com.totwoo.totwoo.utils.IntelligentBleScanner.getInstance(context)
                .startIntelligentScanning();
            LogUtils.d(TAG, "智能BLE扫描已启动");
        } catch (Exception e) {
            LogUtils.e(TAG, "启动智能BLE扫描失败: " + e.getMessage());
        }
    }
    
    /**
     * 通知服务调整策略
     */
    private void notifyServiceAdjustStrategy(Context context, String strategy) {
        try {
            Intent intent = new Intent(context, KeepAliveService.class);
            intent.setAction("ADJUST_STRATEGY");
            intent.putExtra("strategy", strategy);
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent);
            } else {
                context.startService(intent);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "通知服务调整策略失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取蓝牙状态描述
     */
    private String getBluetoothStateDescription(int state) {
        switch (state) {
            case JewInfoSingleton.STATE_UNPAIRED:
                return "未配对";
            case JewInfoSingleton.STATE_DISCONNECTED:
                return "已断开";
            case JewInfoSingleton.STATE_CONNECTED:
                return "已连接";
            case JewInfoSingleton.STATE_RECONNECTING:
                return "重连中";
            default:
                return "未知状态(" + state + ")";
        }
    }
    
    /**
     * 调度WorkManager任务
     */
    public static void scheduleWork(Context context) {
        // 智能约束条件
        Constraints constraints = new Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(false) // 不要求电池不低，我们自己管理
            .setRequiresCharging(false)
            .setRequiresDeviceIdle(false)
            .setRequiresStorageNotLow(true)
            .build();
        
        // 15分钟周期检查
        PeriodicWorkRequest workRequest = new PeriodicWorkRequest.Builder(
            BluetoothKeepAliveWorker.class,
            15, TimeUnit.MINUTES)
            .setConstraints(constraints)
            .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, 1, TimeUnit.MINUTES)
            .addTag(WORK_TAG)
            .build();
        
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            workRequest
        );
        
        LogUtils.d(TAG, "WorkManager任务调度成功");
    }
    
    /**
     * 取消WorkManager任务
     */
    public static void cancelWork(Context context) {
        WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME);
        LogUtils.d(TAG, "WorkManager任务已取消");
    }
    
    /**
     * 获取工作名称
     */
    public static String getWorkName() {
        return WORK_NAME;
    }
    
    /**
     * 获取工作标签
     */
    public static String getWorkTag() {
        return WORK_TAG;
    }

    /**
     * 生成检查报告
     */
    private String generateCheckReport(boolean serviceRestarted, boolean bluetoothReconnected,
                                     String batteryStrategy, long duration) {
        StringBuilder report = new StringBuilder();
        report.append("📋 检查报告:\n");
        report.append("- 服务重启: ").append(serviceRestarted ? "✅ 已重启" : "⭕ 无需重启").append("\n");
        report.append("- 蓝牙重连: ").append(bluetoothReconnected ? "✅ 已重连" : "⭕ 无需重连").append("\n");
        report.append("- 电池策略: ").append(batteryStrategy).append("\n");
        report.append("- 检查耗时: ").append(duration).append("ms");
        return report.toString();
    }
}
