package com.totwoo.totwoo.receiver;

import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.Build;
import android.text.TextUtils;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.service.KeepAliveService;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.worker.BluetoothKeepAliveWorker;

/**
 * 保活系统事件广播接收器
 * 
 * 功能：
 * 1. 监听屏幕开关事件
 * 2. 监听蓝牙状态变化
 * 3. 监听网络状态变化
 * 4. 监听设备重启事件
 * 5. 触发保活机制恢复
 */
public class KeepAliveSystemReceiver extends BroadcastReceiver {

    private static final String TAG = "KeepAliveSystemReceiver";

    // 防抖机制 - 避免频繁触发
    private static long lastScreenEventTime = 0;
    private static long lastBluetoothEventTime = 0;
    private static final long EVENT_DEBOUNCE_INTERVAL = 3000; // 3秒防抖

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null || intent.getAction() == null) return;

        String action = intent.getAction();
        long currentTime = System.currentTimeMillis();
        LogUtils.d(TAG, "📡 收到系统广播: " + action);

        // 检查是否有配对的蓝牙设备
        if (!hasBluetoothJewelry(context)) {
            LogUtils.d(TAG, "没有配对的蓝牙首饰，忽略系统事件");
            return;
        }

        // 防抖检查
        if (isEventDebounced(action, currentTime)) {
            LogUtils.d(TAG, "事件防抖，跳过处理: " + action);
            return;
        }
        
        switch (action) {
            case Intent.ACTION_SCREEN_ON:
                handleScreenOn(context);
                break;
                
            case Intent.ACTION_SCREEN_OFF:
                handleScreenOff(context);
                break;

            case ConnectivityManager.CONNECTIVITY_ACTION:
                handleNetworkStateChanged(context);
                break;
                
            case Intent.ACTION_BOOT_COMPLETED:
            case Intent.ACTION_MY_PACKAGE_REPLACED:
            case Intent.ACTION_PACKAGE_REPLACED:
                handleBootCompleted(context);
                break;
                
            case Intent.ACTION_POWER_CONNECTED:
                handlePowerConnected(context);
                break;
                
            case Intent.ACTION_POWER_DISCONNECTED:
                handlePowerDisconnected(context);
                break;
        }
    }
    
    /**
     * 处理屏幕点亮事件
     */
    private void handleScreenOn(Context context) {
        LogUtils.d(TAG, "屏幕点亮，检查保活状态");
        
        // 屏幕点亮时检查服务状态
        checkAndRestartServices(context, "SCREEN_ON");
        
        // 检查蓝牙连接状态
        checkBluetoothConnection(context);
    }
    
    /**
     * 处理屏幕关闭事件
     */
    private void handleScreenOff(Context context) {
        LogUtils.d(TAG, "屏幕关闭，强化保活机制");
        
        // 屏幕关闭时强化保活
        strengthenKeepAlive(context);
    }
    

    /**
     * 处理网络状态变化
     */
    private void handleNetworkStateChanged(Context context) {
        LogUtils.d(TAG, "网络状态变化，检查保活状态");
        
        // 网络状态变化时检查服务状态
        checkAndRestartServices(context, "NETWORK_CHANGED");
    }
    
    /**
     * 处理设备重启完成
     */
    private void handleBootCompleted(Context context) {
        LogUtils.d(TAG, "设备重启完成，恢复保活机制");
        
        // 延迟启动，等待系统完全启动
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            restoreKeepAliveAfterBoot(context);
        }, 10000); // 延迟10秒
    }
    
    /**
     * 处理电源连接
     */
    private void handlePowerConnected(Context context) {
        LogUtils.d(TAG, "电源已连接，切换到正常模式");
        
        // 电源连接时可以使用更积极的保活策略
        notifyServiceAdjustStrategy(context, "NORMAL");
    }
    
    /**
     * 处理电源断开
     */
    private void handlePowerDisconnected(Context context) {
        LogUtils.d(TAG, "电源已断开，切换到省电模式");
        
        // 电源断开时使用省电策略
        notifyServiceAdjustStrategy(context, "POWER_SAVING");
    }
    
    /**
     * 检查并重启服务
     */
    private void checkAndRestartServices(Context context, String trigger) {
        try {
            Intent serviceIntent = new Intent(context, KeepAliveService.class);
            serviceIntent.setAction("SYSTEM_EVENT_TRIGGER");
            serviceIntent.putExtra("trigger", trigger);
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
            
            LogUtils.d(TAG, "服务检查触发成功: " + trigger);
        } catch (Exception e) {
            LogUtils.e(TAG, "服务检查触发失败: " + e.getMessage());
        }
    }
    
    /**
     * 强化保活机制
     */
    private void strengthenKeepAlive(Context context) {
        try {
            // 通知服务进入锁屏模式
            Intent serviceIntent = new Intent(context, KeepAliveService.class);
            serviceIntent.setAction("SCREEN_LOCK_MODE");
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
            
            LogUtils.d(TAG, "保活机制强化成功");
        } catch (Exception e) {
            LogUtils.e(TAG, "保活机制强化失败: " + e.getMessage());
        }
    }
    
    /**
     * 设备重启后恢复保活
     */
    private void restoreKeepAliveAfterBoot(Context context) {
        try {
            // 1. 启动KeepAliveService
            Intent serviceIntent = new Intent(context, KeepAliveService.class);
            serviceIntent.setAction("BOOT_RESTORE");
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
            
            // 2. 调度WorkManager
            BluetoothKeepAliveWorker.scheduleWork(context);
            
            // 3. 调度JobScheduler
            com.totwoo.totwoo.service.KeepAliveJobService.scheduleJob(context);
            
            LogUtils.d(TAG, "重启后保活恢复成功");
        } catch (Exception e) {
            LogUtils.e(TAG, "重启后保活恢复失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查蓝牙连接状态
     */
    private void checkBluetoothConnection(Context context) {
        try {
            // 检查连接状态并触发重连（如果需要）
            int state = com.totwoo.totwoo.ble.JewInfoSingleton.getInstance().getConnectState();
            if (state != com.totwoo.totwoo.ble.JewInfoSingleton.STATE_CONNECTED) {
                BluetoothManage.getInstance().reconnect(false);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "检查蓝牙连接状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 通知服务调整策略
     */
    private void notifyServiceAdjustStrategy(Context context, String strategy) {
        try {
            Intent intent = new Intent(context, KeepAliveService.class);
            intent.setAction("ADJUST_STRATEGY");
            intent.putExtra("strategy", strategy);
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent);
            } else {
                context.startService(intent);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "通知服务调整策略失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查事件是否需要防抖
     */
    private boolean isEventDebounced(String action, long currentTime) {
        switch (action) {
            case Intent.ACTION_SCREEN_ON:
            case Intent.ACTION_SCREEN_OFF:
                if (currentTime - lastScreenEventTime < EVENT_DEBOUNCE_INTERVAL) {
                    return true;
                }
                lastScreenEventTime = currentTime;
                break;

            case BluetoothAdapter.ACTION_STATE_CHANGED:
                if (currentTime - lastBluetoothEventTime < EVENT_DEBOUNCE_INTERVAL) {
                    return true;
                }
                lastBluetoothEventTime = currentTime;
                break;
        }
        return false;
    }

    /**
     * 检查是否有蓝牙首饰
     */
    private boolean hasBluetoothJewelry(Context context) {
        String jewelryName = PreferencesUtils.getString(context, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        return !TextUtils.isEmpty(jewelryName) && BleParams.isBluetoothJewelry(jewelryName);
    }

    /**
     * 注册系统事件监听
     */
    public static void registerSystemReceiver(Context context) {
        try {
            KeepAliveSystemReceiver receiver = new KeepAliveSystemReceiver();
            IntentFilter filter = new IntentFilter();
            
            // 屏幕事件
            filter.addAction(Intent.ACTION_SCREEN_ON);
            filter.addAction(Intent.ACTION_SCREEN_OFF);
            
            // 蓝牙事件
            filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
            
            // 网络事件
            filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
            
            // 电源事件
            filter.addAction(Intent.ACTION_POWER_CONNECTED);
            filter.addAction(Intent.ACTION_POWER_DISCONNECTED);
            
            context.registerReceiver(receiver, filter);
            LogUtils.d(TAG, "系统事件监听注册成功");
            
        } catch (Exception e) {
            LogUtils.e(TAG, "系统事件监听注册失败: " + e.getMessage());
        }
    }
}
