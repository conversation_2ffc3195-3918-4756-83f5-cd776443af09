package com.totwoo.totwoo.utils;

import android.app.Activity;
import android.app.Dialog;
import android.graphics.Color;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.activity.ComponentActivity;
import androidx.activity.EdgeToEdge;
import androidx.annotation.ColorInt;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;

import com.blankj.utilcode.util.SizeUtils;

/**
 * Edge-to-Edge 适配工具类
 *
 * Android 15 (API 35) 强制执行Edge-to-Edge：
 * - targetSdkVersion=35时，系统自动启用Edge-to-Edge
 * - 使用官方EdgeToEdge.enable()确保向下兼容Android 6+
 * - 统一处理WindowInsets避免内容被系统栏遮挡
 *
 * <AUTHOR>
 * @date 2025-01-05
 * @version 4.0 - Android 15强制执行适配版本
 */
public class EdgeToEdgeUtils {

    /**
     * 为Activity启用Edge-to-Edge
     *
     * Android 15+: 系统强制启用，此调用确保向下兼容
     * Android 6-14: 手动启用Edge-to-Edge
     */
    public static void enableEdgeToEdge(Activity activity) {
        if (activity == null) {
            return;
        }

        // 使用官方EdgeToEdge.enable()方法（需要ComponentActivity）
        // Android 15+会被系统强制启用，这里确保向下兼容
        if (activity instanceof ComponentActivity) {
            EdgeToEdge.enable((ComponentActivity) activity);
        } else {
            // 对于非ComponentActivity，手动设置Edge-to-Edge
            enableEdgeToEdgeManually(activity);
        }
    }

    /**
     * 手动启用Edge-to-Edge（兼容非ComponentActivity）
     */
    private static void enableEdgeToEdgeManually(Activity activity) {
        if (activity == null || activity.getWindow() == null) {
            return;
        }

        Window window = activity.getWindow();
        WindowCompat.setDecorFitsSystemWindows(window, false);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            // Edge-to-Edge模式下，系统栏颜色通过WindowInsets和背景处理
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.VANILLA_ICE_CREAM) {
                // 仅在Android 15以下版本设置透明色
                window.setStatusBarColor(Color.TRANSPARENT);
                window.setNavigationBarColor(Color.TRANSPARENT);
            }
            // Android 15+: 系统自动处理Edge-to-Edge，无需手动设置颜色
        }
    }

    /**
     * 设置状态栏内容颜色（深色/浅色）
     * 兼容Android 35+的新API
     */
    public static void setStatusBarContentDark(Activity activity, boolean isDark) {
        if (activity == null || activity.getWindow() == null) {
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 使用新的WindowInsetsController
            activity.getWindow().getInsetsController().setSystemBarsAppearance(
                isDark ? android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS : 0,
                android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
            );
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6-10 使用旧的SystemUiVisibility
            View decorView = activity.getWindow().getDecorView();
            int flags = decorView.getSystemUiVisibility();
            if (isDark) {
                flags |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            } else {
                flags &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            }
            decorView.setSystemUiVisibility(flags);
        }
    }

    /**
     * 为View设置顶部状态栏Insets处理
     * 适配Android 15强制Edge-to-Edge，避免内容被状态栏遮挡
     */
    public static void setupTopInsets(View view) {
        if (view == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 只处理顶部状态栏，保持原有的左右下边距
            v.setPadding(
                v.getPaddingLeft(),
                systemBars.top,
                v.getPaddingRight(),
                v.getPaddingBottom()
            );

            return insets;
        });
    }

    /**
     * 使用 Margin 方式设置顶部状态栏Insets处理
     * 不影响View内部的padding，适合需要精确控制内部布局的场景
     */
    public static void setupTopInsetsWithMargin(View view) {
        if (view == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 使用 margin 替代 padding
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            if (layoutParams != null) {
                layoutParams.topMargin = systemBars.top;
                v.setLayoutParams(layoutParams);
            }

            return insets;
        });
    }

    /**
     * 使用 Translation 方式设置顶部状态栏Insets处理
     * 通过平移View位置来避开状态栏，不改变布局参数
     */
    public static void setupTopInsetsWithTranslation(View view) {
        if (view == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 使用 translationY 平移View
            v.setTranslationY(systemBars.top);

            return insets;
        });
    }

    /**
     * 使用高度调整方式设置顶部状态栏Insets处理
     * 动态调整View的高度来适配状态栏
     */
    public static void setupTopInsetsWithHeightAdjustment(View view) {
        if (view == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 调整View高度
            ViewGroup.LayoutParams layoutParams = v.getLayoutParams();
            if (layoutParams != null) {
                // 如果原高度是固定值，则增加状态栏高度
                if (layoutParams.height > 0) {
                    layoutParams.height += systemBars.top;
                    v.setLayoutParams(layoutParams);
                }
            }

            return insets;
        });
    }

    /**
     * 为底部导航设置Insets处理
     * 适配Android 15强制Edge-to-Edge，避免底部内容被导航栏遮挡
     */
    public static void setupBottomInsets(View view) {
        if (view == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 为底部导航添加导航栏高度的下边距
            v.setPadding(
                v.getPaddingLeft(),
                v.getPaddingTop(),
                v.getPaddingRight(),
                systemBars.bottom
            );

            return insets;
        });
    }

    /**
     * 为底部导航设置Insets处理
     * 适配Android 15强制Edge-to-Edge，避免底部内容被导航栏遮挡
     */
    public static void setupBottomInsetsForLoveHomeAct(View view) {
        if (view == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 为底部导航添加导航栏高度的下边距
            v.setPadding(
                    v.getPaddingLeft(),
                    v.getPaddingTop(),
                    v.getPaddingRight(),
                    systemBars.bottom + SizeUtils.dp2px(56)
            );

            return insets;
        });
    }
    /**
     * 为内容区域设置完整的Insets处理
     * 适配Android 15强制Edge-to-Edge，避免内容被系统栏遮挡
     *
     * 统一处理方案：
     * - 替代XML中的android:fitsSystemWindows="true"
     * - 替代TopBar的单独Insets处理
     * - 为整个内容区域提供统一的上下边距
     */
    public static void setupContentInsets(View contentView) {
        if (contentView == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(contentView, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 为内容区域设置上下边距，避免被状态栏和导航栏遮挡
            // 这将确保TopBar不被状态栏遮挡，底部内容不被导航栏遮挡
            v.setPadding(
                v.getPaddingLeft(),
                systemBars.top,
                v.getPaddingRight(),
                systemBars.bottom
            );

            return insets;
        });
    }

    /**
     * 为内容区域设置只处理顶部的Insets处理
     * 适用于有底部Tab的Activity（如HomeActivity）
     */
    public static void setupContentInsetsTopOnly(View contentView) {
        if (contentView == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(contentView, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 只处理顶部状态栏，保持原有的底部边距
            // 适用于有底部Tab的Activity
            v.setPadding(
                v.getPaddingLeft(),
                systemBars.top,
                v.getPaddingRight(),
                v.getPaddingBottom()
            );

            return insets;
        });
    }

    /**
     * 设置状态栏颜色与TopBar一体显示
     *
     * Android 15适配：简化处理弃用API
     */
    public static void setStatusBarColorForUnifiedTopBar(Activity activity, @ColorInt int color) {
        if (activity == null || activity.getWindow() == null) {
            return;
        }

        // Android 15适配：仅在Android 14及以下使用setStatusBarColor
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP &&
            Build.VERSION.SDK_INT < Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            activity.getWindow().setStatusBarColor(color);
        }
        // Android 15+: 弃用API，通过TopBar背景扩展实现一体化效果

        // 设置状态栏内容颜色（所有版本都支持）
        boolean isLightColor = isLightColor(color);
        setStatusBarContentDark(activity, isLightColor);
    }

    /**
     * 判断颜色是否为浅色
     */
    private static boolean isLightColor(@ColorInt int color) {
        int red = Color.red(color);
        int green = Color.green(color);
        int blue = Color.blue(color);
        double brightness = (red * 0.299 + green * 0.587 + blue * 0.114);
        return brightness > 128;
    }

    /**
     * 为Dialog启用Edge-to-Edge
     *
     * Android 15+: Activity强制Edge-to-Edge，Dialog需要手动适配
     * Android 6-14: 手动启用Dialog的Edge-to-Edge
     */
    public static void enableEdgeToEdgeForDialog(Dialog dialog) {

    }

    /**
     * 专门为有输入框的Activity提供IME适配
     * 正确处理Android 15的IME Insets，避免输入框被软键盘遮挡
     *
     * @param view 需要适配的View（通常是内容区域或根布局）
     */
    public static void setupBottomIMEInsets(View view) {
        if (view == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // Android 15+: 正确处理IME Insets
            Insets imeInsets = insets.getInsets(WindowInsetsCompat.Type.ime());

            // 计算实际需要的底部边距
            // 键盘弹出时使用键盘高度，否则使用导航栏高度
            int bottomInset = systemBars.bottom + imeInsets.bottom;

            v.setPadding(
                v.getPaddingLeft(),
                v.getPaddingTop(),
                v.getPaddingRight(),
                bottomInset
            );

            // 返回insets让其他View也能处理（如果需要的话）
            return insets;//return WindowInsetsCompat.CONSUMED; // 或直接 return insets，看你是否要下传
        });
    }


    /**
     * 为输入框Activity提供传统的系统适配方案
     * 恢复系统的软键盘适配，最稳定可靠
     *
     * @param activity 需要适配的Activity
     */
    public static void setFitsSystemWindows(Activity activity) {
        if (activity == null) {
            return;
        }

        // 恢复系统的软键盘适配
        WindowCompat.setDecorFitsSystemWindows(activity.getWindow(), true);
    }

    /**
     * 专门为 TopBar 优化的 Edge-to-Edge 适配
     * 使用 margin 方式，不影响内部图标的 padding
     */
    public static void setupTopBarEdgeToEdge(View topBar) {
        if (topBar == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(topBar, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 使用 margin 推下整个 TopBar，不影响内部元素的 padding
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            if (layoutParams != null) {
                layoutParams.topMargin = systemBars.top;
                v.setLayoutParams(layoutParams);
            }

            return insets;
        });
    }

    /**
     * 为 TopBar 设置状态栏背景色适配
     * 在状态栏区域添加背景色，TopBar 保持原有位置和内部布局
     */
    public static void setupTopBarWithStatusBarBackground(View topBar, int backgroundColor) {
        if (topBar == null) {
            return;
        }

        ViewCompat.setOnApplyWindowInsetsListener(topBar, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 为 TopBar 添加顶部 padding 作为状态栏背景区域
            v.setPadding(
                v.getPaddingLeft(),
                systemBars.top,
                v.getPaddingRight(),
                v.getPaddingBottom()
            );

            // 设置背景色延伸到状态栏区域
            v.setBackgroundColor(backgroundColor);

            return insets;
        });
    }


}
