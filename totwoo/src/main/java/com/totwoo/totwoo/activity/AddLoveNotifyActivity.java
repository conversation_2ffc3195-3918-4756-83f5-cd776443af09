package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.hjq.shape.view.ShapeTextView;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.bean.CustomItemBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.WheelView;
import com.totwoo.totwoo.widget.pickerview.TimePickerDialog;
import com.totwoo.totwoo.widget.pickerview.data.Type;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public class AddLoveNotifyActivity extends BaseActivity {

    @BindView(R.id.add_love_color_library_rv)
    RecyclerView colorLibraryRecyclerView;
    @BindView(R.id.add_love_title_edittext)
    EditText mTitleEdittext;

    @BindView(R.id.edit_custom_long_vibration_tv)
    ShapeTextView mLongVibrationTv;
    @BindView(R.id.edit_custom_short_vibration_tv)
    ShapeTextView mShortVibrationTv;

    @BindView(R.id.short_vibration_iv)
    View mShortVibrationIv;

    @BindView(R.id.long_vibration_iv)
    View mLongVibrationIv;

    @BindView(R.id.add_love_birthday_value_tv)
    TextView mBirthdayValueTv;
    @BindView(R.id.add_love_notify_value_tv)
    TextView mNotifyTimeTv;
    @BindView(R.id.add_love_vibration_ll)
    ViewGroup mVibrationLl;
    @BindView(R.id.add_love_notify_mode_title)
    TextView mModeTitle;

    SimpleDateFormat ymdFormat = new SimpleDateFormat("yyyy-MM-dd");

    private boolean isChange = false;
    private CustomColorLibraryAdapter colorLibraryAdapter;
    private List<String> defaultAnnNotifyTimeStrs;
    private CustomItemBean currentBean;

    private final static double DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[] = {24, 48, 72, 168};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_love_notify);
        ButterKnife.bind(this);

        String tempBirth = ymdFormat.format(System.currentTimeMillis());

        currentBean = new CustomItemBean();
        currentBean.setRemind_time(DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[1] + "");
        currentBean.setRepeat_notify("0");
        currentBean.setDefine_time(tempBirth);
        currentBean.setShock_type(JewelryNotifyModel.LONG);
        currentBean.setNotify_mode("RED");

        initView();
        int birthSize = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES.length;

        defaultAnnNotifyTimeStrs = new ArrayList<>();
        for (int i = 0; i < birthSize; i++) {
            defaultAnnNotifyTimeStrs.add(getAnnHourStringByIndex(i));
        }

        if (BleParams.isButtonBatteryJewelry()) {
            mVibrationLl.setVisibility(View.GONE);
            mModeTitle.setText(R.string.love_notify_instruction_no_vibrate);
        }
    }

    private void initView() {
        mTitleEdittext.setFilters(new InputFilter[]{
                new InputFilter.LengthFilter(10)
        });
        switch (currentBean.getShock_type()) {
            case JewelryNotifyModel.LONG:
                setTextColorBtn(true);
                break;
            case JewelryNotifyModel.SHORT:
                setTextColorBtn(false);
                break;
        }

        StringBuffer buffer = new StringBuffer(currentBean.getDefine_time());
        String year = buffer.substring(0, 4);
        String month = buffer.substring(5, 7);
        String day = buffer.substring(8, 10);
        mBirthdayValueTv.setText(year + "-" + month + "-" + day);
        mNotifyTimeTv.setText(getAnnHourStringByIndex(getBirthHourIndexByDouble(Double.valueOf(currentBean.getRemind_time()))));

        boolean jewelryGlitterEnabled = !(BleParams.isCtJewlery() || BleParams.isMWJewlery()) || PreferencesUtils.getBoolean(this, "jewelry_glitter_enabled", true);
        if (!jewelryGlitterEnabled) {
            colorLibraryRecyclerView.setAlpha(0.4f);
        } else {
            colorLibraryRecyclerView.setAlpha(1f);
        }
        int spanCount = BleParams.isCtJewlery() ? 7 : 6;
        colorLibraryRecyclerView.setLayoutManager(new GridLayoutManager(this, spanCount));
        colorLibraryAdapter = new CustomColorLibraryAdapter(currentBean.getNotify_mode(), spanCount, false,true);

        colorLibraryAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (!jewelryGlitterEnabled) {
                ToastUtils.showLong(this, R.string.enable_notification_can_flash);
                return;
            }
            ColorLibraryBean colorLibraryBean = colorLibraryAdapter.getItem(position);
            if (colorLibraryBean != null) {
                currentBean.setNotify_mode(colorLibraryBean.getColor());//颜色名字
                isChange = true;
                colorLibraryAdapter.setSelectColor(colorLibraryBean.getColor());
                saveNowModel();
            }
        });

        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> quit());
        setTopTitle(getString(R.string.love_space_notify_add_title));
        setTopTitleColor(getResources().getColor(R.color.text_color_black_important));
    }

    @OnClick({R.id.edit_custom_long_vibration_tv, R.id.edit_custom_short_vibration_tv,
            R.id.add_love_notify_layout, R.id.add_love_birthday_layout, R.id.add_love_save_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.edit_custom_long_vibration_tv:
                isChange = true;
                currentBean.setShock_type(JewelryNotifyModel.LONG);
                saveNowModel();
                setTextColorBtn(true);
                break;

            case R.id.edit_custom_short_vibration_tv:
                isChange = true;
                currentBean.setShock_type(JewelryNotifyModel.SHORT);
                saveNowModel();
                setTextColorBtn(false);
                break;
            case R.id.add_love_notify_layout:
                showAnnNotifyDialog();
                break;
            case R.id.add_love_birthday_layout:
                try {
                    showAnnDialog();
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                break;
            case R.id.add_love_save_tv:
                saveInfo();
                break;
        }
    }

    private void saveInfo() {
        String title = mTitleEdittext.getText().toString().trim();
        if (TextUtils.isEmpty(title)) {
            ToastUtils.showShort(AddLoveNotifyActivity.this, getString(R.string.custom_notify_input_title));
            return;
        }

        HttpHelper.customService.saveCustom(1, 5, title, currentBean.getDefine_time(),
                        currentBean.getRemind_time(), currentBean.getRepeat_notify(),
                        currentBean.getShock_type(), currentBean.getNotify_mode(), ToTwooApplication.owner.getPairedId(), ToTwooApplication.otherPhone)
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<CustomItemBean>>() {
                    @Override
                    public void onCompleted() {
//                        if(!TextUtils.equals(getIntent().getStringExtra("from"),"list")){
//                            startActivity(new Intent(AddLoveNotifyActivity.this,CustomNotifyListActivity.class));
//                        }
                        finish();
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(AddLoveNotifyActivity.this, R.string.error_net);
                    }

                    @Override
                    public void onNext(HttpBaseBean<CustomItemBean> customItemBeanHttpBaseBean) {
                        if (customItemBeanHttpBaseBean.getErrorCode() == 0) {
//                            CustomItemBean customItemBean = customItemBeanHttpBaseBean.getData();
//                            customItemBean.setDefine_time(currentBean.getDefine_time());
//                            CustomNotifyDbHelper.getInstance().addBean(customItemBean);
                            //TODO
//                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ADD_SUCCESSED, null);
                            if (TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), "space")) {
                                startActivity(new Intent(AddLoveNotifyActivity.this, LoveNotifyListActivity.class));
                            }
                        }
                    }
                });

    }

    private void quit() {
        if (isChangeThings()) {
            final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(AddLoveNotifyActivity.this);
            commonMiddleDialog.setMessage(R.string.custom_order_tips);
            commonMiddleDialog.setSure(v -> {
                finish();
                commonMiddleDialog.dismiss();
            });
            commonMiddleDialog.setCancel(R.string.cancel);
            commonMiddleDialog.show();
        } else {
            finish();
        }
    }

    private boolean isChangeThings() {
        if (isChange) {
            return true;
        }
        if (!TextUtils.isEmpty(mTitleEdittext.getText().toString().trim())) {
            return true;
        }
        return false;
    }

    private void saveNowModel() {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            Toast.makeText(this, R.string.error_jewelry_connect, Toast.LENGTH_SHORT).show();
            return;
        }
        JewelryNotifyModel jewelryNotifyModel = new JewelryNotifyModel();
        jewelryNotifyModel.setFlashColor(currentBean.getNotify_mode());
        if (TextUtils.equals(currentBean.getShock_type(), JewelryNotifyModel.LONG))
            jewelryNotifyModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
        else
            jewelryNotifyModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);

        BluetoothManage.getInstance().notifyJewelry(jewelryNotifyModel.getVibrationSeconds(), jewelryNotifyModel.getFlashColorValue());
    }

    private void setTextColorBtn(boolean isLong) {
        if (isLong) {
            mLongVibrationIv.setVisibility(View.VISIBLE);
            mShortVibrationIv.setVisibility(View.GONE);

            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        } else {
            mLongVibrationIv.setVisibility(View.GONE);
            mShortVibrationIv.setVisibility(View.VISIBLE);

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        }
    }

    private void showAnnDialog() throws ParseException {
        final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        long thirtyYears = 30L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack((timePickerView, millseconds) -> {
                    String tempBirthday = format.format(millseconds);
                    isChange = true;
                    currentBean.setDefine_time(tempBirthday);
                    StringBuffer buffer = new StringBuffer(tempBirthday);
                    String year = buffer.substring(0, 4);
                    String month = buffer.substring(5, 7);
                    String day = buffer.substring(8, 10);
                    mBirthdayValueTv.setText(year + "-" + month + "-" + day);

                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
//                .setTitleStringId(getString(R.string.love_space_notify_select_date))
//                .setInfoText(getString(R.string.love_notify_remind_time))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis())
                .setMaxMillseconds(System.currentTimeMillis() + thirtyYears)
                .setCurrentMillseconds(format.parse(currentBean.getDefine_time()).getTime())
                .setThemeColor(getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getSupportFragmentManager(), "year_month_day");
    }

    private void showAnnNotifyDialog() {
        final CustomDialog dialog = new CustomDialog(AddLoveNotifyActivity.this);
//        dialog.setTitle(getString(R.string.custom_notify_remind_time));
        LinearLayout weekLayout = new LinearLayout(this);
        weekLayout.setLayoutParams(new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekLayout.setOrientation(LinearLayout.HORIZONTAL);

        weekLayout.setPadding(Apputils.dp2px(this, 20), 0,
                Apputils.dp2px(this, 20), 0);
        final WheelView weekWheelView = new WheelView(this);
        weekWheelView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        weekWheelView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        weekWheelView.setItems(defaultAnnNotifyTimeStrs, 3,
                "");
        // 获取当前设置的时间去设置wheelview
        weekWheelView.setSeletion(getBirthHourIndexByDouble(Double.valueOf(currentBean.getRemind_time())));
        weekLayout.addView(weekWheelView);
        dialog.setMainLayoutView(weekLayout);
        // dialog.setMessage(R.string.totwoo_send_instruction);
        dialog.setNegativeButtonText(R.string.cancel);
        dialog.setPositiveButton(R.string.confirm, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                ToastUtils.showDebug(SedentaryReminderActivity.this,
//                        wheelView.getSeletedItem(), 3000);
                String str = weekWheelView.getSeletedItem();
                isChange = true;
                mNotifyTimeTv.setText(str);
                currentBean.setRemind_time(DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[defaultAnnNotifyTimeStrs.indexOf(str)] + "");
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    private int getBirthHourIndexByDouble(double d) {
        int index = 0;
        int size = DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES.length;
        for (int i = 0; i < size; i++) {
            if (d == DEFAULT_BIRTH_NOTIFY_TIME_DOUBLES[i]) {
                index = i;
            }
        }
        return index;
    }

    private String getAnnHourStringByIndex(int index) {
        String string = getString(R.string.custom_notify_remind_1d);
        switch (index) {
            case 0:
//                string = getString(R.string.love_space_notify_remind_day);
                string = getString(R.string.custom_notify_remind_1d);
                break;
            case 1:
                string = getString(R.string.custom_notify_remind_2d);
                break;
            case 2:
                string = getString(R.string.custom_notify_remind_3d);
                break;
            case 3:
                string = getString(R.string.custom_notify_remind_7d);
                break;
            case 4:
                break;
        }
        return string;
    }

    @Override
    protected void onResume() {
        super.onResume();
        BluetoothManage.getInstance().connectedStatus();
    }
}
