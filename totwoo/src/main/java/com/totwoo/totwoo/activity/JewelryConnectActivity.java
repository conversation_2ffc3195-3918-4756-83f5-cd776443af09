package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.activity.CallRemindSetActivity.ALL_CALL_REMIND_SWITCH_KEY;
import static com.totwoo.totwoo.activity.CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_SWITCH_KEY;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.window.OnBackInvokedDispatcher;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.Utils;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.giftMessage.GiftDataActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.GiftCardReceiveBean;
import com.totwoo.totwoo.bean.GiftMessageBean;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BleUtils;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.nordic.ScanManager;
import com.totwoo.totwoo.service.TotwooPlayService;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;

/**
 * 硬件连接界面, 提供链接硬件手势的入口
 *
 * <AUTHOR>
 */
@SuppressLint("NonConstantResourceId")
public class JewelryConnectActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.jewelry_connect_anim)
    ImageView jewConnectAnim;
    @BindView(R.id.jew_con_search_info_view)
    LinearLayout searInfoView;
    @BindView(R.id.jew_con_search_info_tv)
    TextView searInfoTv;
    @BindView(R.id.jewelry_connect_new_connectin)
    TextView newConnecting;
    @BindView(R.id.jewelry_connect_fail_layout)
    RelativeLayout connectFailLayout;
    @BindView(R.id.jew_con_content_view_layout)
    ConstraintLayout jewConnctcontentView;
    @BindView(R.id.jew_con_click_hint_tv)
    TextView mClickHint;


    private int connectingDotCount = 0;
    private boolean isDestroyed = false;
    private boolean needScan = false;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_jewelry_connect);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        BluetoothManage.getInstance().setSkip(false);

        initConnectingText();

        checkSystemLocationAndScan();

//        if (BuildConfig.DEBUG) {
//            findViewById(R.id.jew_con_skip).setOnLongClickListener(v -> {
//                BluetoothManage.getInstance().testConnectJewelry("TWO84", "F1:CC:17:12:12:00");
//                return false;
//            });
//        }

        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.JEW_CONNECT);
    }

    private void initConnectingText() {
        if (connectingDotCount == 6) {
            connectingDotCount = 1;
        } else {
            connectingDotCount++;
        }
        mHandler.postDelayed(() -> {
            String res = getResources().getString(R.string.bluetooth_searching);
            for (int i = 0; i < connectingDotCount; i++)
                res = res + ".";
            newConnecting.setText(res);

            initConnectingText();
        }, 600);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (locationCustomDialog != null && locationCustomDialog.isShowing()
                && CommonUtils.isLocServiceEnable(JewelryConnectActivity.this)) {
            locationCustomDialog.dismiss();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isFinishing()) {
            destroy();
        }
    }

    private void destroy() {
        if (isDestroyed) {
            return;
        }
        InjectUtils.injectUnregisterListenerAll(this);
        if (isUnPair && isStart) {
            BluetoothManage.getInstance().reconnect(false);
        }
        // 回收资源
        isDestroyed = true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        destroy();
    }


    @OnClick({R.id.jew_con_skip, R.id.jew_con_view_help_btn, R.id.connect_fail_again, R.id.jew_con_click_hint_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.jew_con_skip:
                goNext(false);
                break;
            case R.id.jew_con_view_help_btn:
                WebViewActivity.loadUrl(JewelryConnectActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_HELP), false);
                break;
            case R.id.connect_fail_again:
                connectFailLayout.setVisibility(View.GONE);
                checkSystemLocationAndScan();
                break;
            case R.id.jew_con_click_hint_tv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CONNECT_CLICK_HOWTOCLICKTWO);
                WebViewActivity.loadUrl(JewelryConnectActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_TOUCH_HINT), false);
                break;
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        back();
    }

    @NonNull
    @Override
    public OnBackInvokedDispatcher getOnBackInvokedDispatcher() {
        return super.getOnBackInvokedDispatcher();
    }

    private void back() {
        if (isUnPair) {
            if (isStart) {
                BluetoothManage.getInstance().skipScan();
                resetInfo();
                finish();
            } else {
                ToastUtils.showShort(JewelryConnectActivity.this, R.string.operate_too_fast);
            }
        } else {
            finish();
        }
    }

    private Animation animSearchInfo;

    private void startSearchingAnim() {
        setAndStartConnectAnim(R.drawable.jewelry_connecting);
        animSearchInfo = AnimationUtils.loadAnimation(this, R.anim.jewelry_connect_apear);
        searInfoView.startAnimation(animSearchInfo);
    }

    private void setAndStartConnectAnim(int drawableId) {
        jewConnectAnim.setImageDrawable(ContextCompat.getDrawable(this, drawableId));
        AnimationDrawable ad = (AnimationDrawable) jewConnectAnim.getDrawable();
        if (drawableId == R.drawable.jewelry_connect_success)
            ad.setOneShot(true);
        else
            ad.setOneShot(false);
        ad.start();
    }


    private void goNext(boolean isSuccess) {
        //登录页面跳转逻辑
        if (TextUtils.equals(getIntent().getStringExtra(CommonArgs.FROM_TYPE), CommonArgs.LOGIN)) {
            if (isSuccess) {
                HttpHelper.card.getGift(ToTwooApplication.owner.getTotwooId(), ToTwooApplication.owner.getPhone(), PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))
                        .compose(HttpHelper.rxSchedulerHelper())
                        .subscribe(new Observer<HttpBaseBean<GiftCardReceiveBean>>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {
                                finishInfoOrStart();
                                overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
                                finish();
                            }

                            @Override
                            public void onNext(HttpBaseBean<GiftCardReceiveBean> giftCardReceiveBeanHttpBaseBean) {
                                if (giftCardReceiveBeanHttpBaseBean.getErrorCode() == 0 && giftCardReceiveBeanHttpBaseBean.getData().isHave()) {
                                    GiftMessageBean giftMessageBean = new GiftMessageBean();
                                    giftMessageBean.setGreetingCardId(giftCardReceiveBeanHttpBaseBean.getData().getGreetingCardId());
                                    giftMessageBean.setSenderId(giftCardReceiveBeanHttpBaseBean.getData().getSenderId());
                                    giftMessageBean.setSenderName(giftCardReceiveBeanHttpBaseBean.getData().getSenderName());
                                    giftMessageBean.setGreetingCardType(giftCardReceiveBeanHttpBaseBean.getData().getGreetingCardType());
                                    giftMessageBean.setSendTime(giftCardReceiveBeanHttpBaseBean.getData().getSendTime());
                                    giftMessageBean.setGreetingCardData(giftCardReceiveBeanHttpBaseBean.getData().getGreetingCardData());
                                    Intent intent = new Intent(JewelryConnectActivity.this, GiftDataActivity.class);
                                    intent.putExtra(CommonArgs.FROM_TYPE, GiftDataActivity.RECEIVER);
                                    intent.putExtra(GiftDataActivity.OPEN_FROM_TYPE, CommonArgs.LOGIN);
                                    intent.putExtra(GiftDataActivity.ITEM, giftMessageBean);
                                    startActivity(intent);
                                    finish();
                                } else {
                                    finishInfoOrStart();
                                    overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
                                    finish();
                                }
                            }
                        });
            } else {
                finishInfoOrStart();
                overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
                finish();
            }
        }
        //连接入口页跳转逻辑
        else {
            if (isSuccess) {
                LogUtils.e("HomeActivityControl");
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ORDER_UPDATE, null);
                HomeActivityControl.getInstance().connectJew(JewelryConnectActivity.this);
                overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
                finish();
            } else {
                if (isUnPair) {
                    if (isStart) {
                        BluetoothManage.getInstance().skipScan();
                        resetInfo();
                        overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
                        finish();
                    } else {
                        ToastUtils.showShort(JewelryConnectActivity.this, R.string.operate_too_fast);
                    }
                } else {
                    overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
                    finish();
                }
            }
        }
    }

    private void finishInfoOrStart() {
        if (ToTwooApplication.isInfoSetFinish(ToTwooApplication.owner)) {
            HomeActivityControl.getInstance().openHomeActivity(JewelryConnectActivity.this);
        } else {
            startActivity(new Intent(this, InitInfoActivity.class).putExtra(InitInfoActivity.INIT_INFO, true));
        }
    }

    private boolean isUnPair;


    private void checkSystemLocationAndScan() {
        if (PermissionUtil.hasSystemLocation()) {
            toStartScan();
        } else {
            showLocationDenyDialog();
        }
    }

    private void toStartScan() {

        CommonUtils.setStateBar(this, true);
        startSearchingAnim();

        String nameOld = PreferencesUtils.getString(JewelryConnectActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        if (TextUtils.isEmpty(nameOld)) {
            // Nordic BLE组件
            ScanManager scanManager =  ScanManager.INSTANCE;
            scanManager.startDeviceDiscoveryScan(new com.totwoo.totwoo.ble.nordic.ScanManager.ScanCallback() {
                @Override
                public void onAddressDeviceFound(@NonNull BluetoothDevice device) {
                }

                @Override
                public void onOTADeviceFound(@NonNull BluetoothDevice device) {
                }

                @SuppressLint("MissingPermission")
                @Override
                public void onDeviceFound(android.bluetooth.BluetoothDevice device, int rssi, no.nordicsemi.android.support.v18.scanner.ScanRecord scanRecord) {

                    //安全首饰的需要进入首页和设备imei一起传
                    if (!BleParams.isSecurityJewlery()) {
                        showProgressDialog();
                        launchRequestWithFlexibleError(
                                HttpHelper.commonService.bindState(device.getName(), "", device.getAddress(), "connect"),
                                objectHttpBaseBean -> {
                                    launchRequestWithFlexibleError(
                                            HttpHelper.multiJewelryService.addJewelry("", device.getAddress(), device.getName(), "", "", 1, ""),
                                            addJewelryHttpBaseBean -> {
                                                try {
                                                    LocalJewelryInfo info = new LocalJewelryInfo(device.getAddress(), device.getName(), 1, System.currentTimeMillis());
                                                    LocalJewelryDBHelper.getInstance().addBean(info);
                                                    if (LocalJewelryDBHelper.getInstance().getAllBeans().size() > 1) {
                                                        LocalJewelryDBHelper.getInstance().setSelected(device.getAddress());
                                                    }
                                                    com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_EMOTION, null);
                                                    if (BleParams.isButtonBatteryJewelry()) {
                                                        PreferencesUtils.put(JewelryConnectActivity.this, ALL_CALL_REMIND_SWITCH_KEY, false);
                                                        PreferencesUtils.put(JewelryConnectActivity.this, IMPORTANT_CONTACT_REMIND_SWITCH_KEY, true);
                                                    }

                                                    // 保存Mac地址
                                                    PreferencesUtils.put(JewelryConnectActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, device.getAddress());
                                                    // 保存连接设备类型
                                                    PreferencesUtils.put(JewelryConnectActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, device.getName());
                                                    BluetoothManage.getInstance().startBackgroundScan();

                                                    dismissProgressDialog();

                                                    handler.sendEmptyMessage(0);
                                                    BluetoothManage.getInstance().reconnect(false);
                                                    mHandler.postDelayed(() -> goNext(true), 2000);
                                                } catch (DbException e) {
                                                    e.printStackTrace();
                                                    Log.e("xLog", "SQ add error" );
                                                }
                                            },
                                            fail -> {
                                                dismissProgressDialog();
                                                return false;
                                            },
                                            false
                                    );
                                },
                                fail -> {
                                    dismissProgressDialog();
                                    return false;
                                },
                                false
                        );
                    } else {

                    }
                }

                @Override
                public void onScanTimeout() {
                    handleScanFail();
                }

                @Override
                public void onScanFailed(int errorCode) {
                    handleScanFail();
                }
            });


        } else {
            isUnPair = true;
            BluetoothManage.getInstance().unPair();
        }
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_APART, runThread = TaskType.UI)
    public void jewrlyApartSuccess(EventData data) {
        isStart = true;
        toStartScan();
    }


    //切换，解绑成功
    @EventInject(eventType = S.E.E_JEWERLY_APART_FAIL, runThread = TaskType.UI)
    public void jewrlyApartFail(EventData data) {
        dismissProgressDialog();
        ToastUtils.showLong(Utils.getApp(), "unbind fail,try again");
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == PermissionUtil.REQUEST_OPEN_SYS_LOCATION) {
            if (PermissionUtil.hasSystemLocation()) {
                toStartScan();
            } else {
                ToastUtils.showShort(this, R.string.gps_connect_request_hint);
            }
        }
    }

    private boolean isStart;



    private void handleScanFail() {
        animSearchInfo.cancel();
        connectFailLayout.setVisibility(View.VISIBLE);
        CommonUtils.setStateBar(this, false);

//        if (isSameName) {
//            final CommonMiddleDialog dialog = new CommonMiddleDialog(this);
//            dialog.setMessage(R.string.jewelry_add_same_name);
//            dialog.setSure(R.string.i_know, v -> dialog.dismiss());
//            dialog.show();
//            isSameName = false;
//        }
    }



    @EventInject(eventType = S.E.E_BLUETOOTH_OPEN, runThread = TaskType.UI)
    public void bluetoothOpen(EventData data) {
        if (needScan) {
            checkSystemLocationAndScan();
            needScan = false;
        }
    }


    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {
    }

    /**
     * 展示请求蓝牙开启的对话框
     */
    public void showBluetoothDialog() {
        final CustomDialog dialog = new CustomDialog(this);
        dialog.setMessage(R.string.request_open_bluetooth);
        dialog.setPositiveButton(R.string.allow, v -> {
            BleUtils.enableBlueTooth(JewelryConnectActivity.this);
            dialog.dismiss();
        });
        dialog.setNegativeButton(R.string.cancel, v -> dialog.dismiss());

        dialog.show();
    }

    @SuppressLint("HandlerLeak")
    Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
//            super.handleMessage(msg);
            setAndStartConnectAnim(R.drawable.jewelry_connect_success);
            searInfoTv.setText(R.string.connect_successfull);
            mClickHint.setVisibility(View.GONE);
            newConnecting.setVisibility(View.GONE);
            startService(new Intent(JewelryConnectActivity.this, TotwooPlayService.class));
        }
    };

    private void resetInfo() {
        String name = PreferencesUtils.getString(JewelryConnectActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        try {
            List<LocalJewelryInfo> infos = LocalJewelryDBHelper.getInstance().getAllBeans();
            LocalJewelryInfo selectInfo = LocalJewelryDBHelper.getInstance().getSelectedBean();
            if (TextUtils.isEmpty(name) && infos.size() > 0 && !TextUtils.isEmpty(selectInfo.getName())) {
                PreferencesUtils.put(JewelryConnectActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, selectInfo.getMac_address());
                PreferencesUtils.put(JewelryConnectActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, selectInfo.getName());
                PreferencesUtils.put(JewelryConnectActivity.this, BleParams.PAIRED_FIRST_CONNECT, true);
                BluetoothManage.getInstance().startBackgroundScan();
//                JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
            }
        } catch (DbException e) {
            e.printStackTrace();
        }
    }


    private CustomDialog locationCustomDialog;

    private void showLocationDenyDialog() {
        if (locationCustomDialog == null) {
            locationCustomDialog = new CustomDialog(JewelryConnectActivity.this);
            locationCustomDialog.setCanceledOnTouchOutside(false);
            locationCustomDialog.setTitle(R.string.tips);
            locationCustomDialog.setMessage(R.string.gps_connect_request_hint);
            locationCustomDialog.setPositiveButton(R.string.open_hint, v -> {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CONNECT_LOCATION_SWITCH_SET);
                startActivityForResult(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS), PermissionUtil.REQUEST_OPEN_SYS_LOCATION);
            });
            locationCustomDialog.setNegativeButton(R.string.cancel, v -> {
                locationCustomDialog.dismiss();
                finish();
            });
        }
        locationCustomDialog.show();
    }
}
