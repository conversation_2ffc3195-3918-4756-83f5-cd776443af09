package com.totwoo.totwoo.activity;

import android.os.Bundle;

import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;

/**
 * Created by lixingmao on 2017/4/6.
 */

public class TestActivity extends BaseActivity {
    /**
     * 启动CustomOrderActivity
     CustomOrderActivity.onStart(L:324): order onStart
     CustomOrderActivity.onResume(L:335): order onResume

     启动TestActivity
     CustomOrderActivity.onPause(L:341): order onPause
     TestActivity.onCreate(L:17): order onCreate
     TestActivity.onStart(L:22): order onStart
     TestActivity.onResume(L:33): order onResume

     返回CustomOrderActivity，销毁TestActivity
     TestActivity.onPause(L:39): order onPause
     CustomOrderActivity.onResume(L:335): order onResume
     TestActivity.onStop(L:45): order onStop
     TestActivity.onDestroy(L:51): order onDestroy
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().getDecorView().setBackgroundResource(R.color.layer_bg_white);
        LogUtils.e("order onCreate");
    }
    @Override
    protected void onStart() {
        super.onStart();
        LogUtils.e("order onStart");
    }
    @Override
    protected void onRestart() {
        super.onRestart();
        LogUtils.e("order onRestart");
    }

    @Override
    protected void onResume() {
        super.onResume();
        LogUtils.e("order onResume");
    }

    @Override
    protected void onPause() {
        super.onPause();
        LogUtils.e("order onPause");
    }

    @Override
    protected void onStop() {
        super.onStop();
        LogUtils.e("order onStop");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LogUtils.e("order onDestroy");
    }

}
